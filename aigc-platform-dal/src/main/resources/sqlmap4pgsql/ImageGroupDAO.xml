<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="image_ids" jdbcType="OTHER" property="imageIds" />
    <result column="metadata" jdbcType="OTHER" property="metadata" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <resultMap id="CaptionResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="image_ids" jdbcType="OTHER" property="imageIds" />
    <result column="caption" jdbcType="OTHER" property="caption" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, image_ids, metadata, metadata, create_time, modify_time, deleted
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageGroupExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from image_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        LIMIT ${rows} OFFSET ${offset}
      </if>
      <if test="offset == null">
        LIMIT ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from image_group
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAllTags" resultType="java.lang.String">
    SELECT DISTINCT tag
    FROM image_group,
    jsonb_array_elements_text(metadata->'tags') AS tag
    WHERE NOT deleted
  </select>
  <select id="selectAllImageIdsNotCaptionByUser" resultType="java.lang.Integer">
    SELECT DISTINCT (img_id.value)::int AS image_id
    FROM public.image_group ig
    INNER JOIN public.image_group_caption_user igcu
        ON ig.id = igcu.image_group_id
        AND igcu.deleted = false
    CROSS JOIN jsonb_array_elements(ig.image_ids) AS img_id(value)
    WHERE
        ig.deleted = false
        AND jsonb_typeof(ig.image_ids) = 'array'
        AND img_id.value IS NOT NULL
        AND NOT EXISTS (
            SELECT 1
            FROM public.image_caption_user icu
            WHERE icu.image_id = (img_id.value)::int
              AND icu.user_id = #{userId}
              AND icu.deleted = false
        )
  </select>
  <select id="selectAllByTotalScoreSame" parameterType="map" resultMap="CaptionResultMap">
    SELECT
      ig.id, igc.caption, ig.image_ids
    FROM
      image_group_caption igc
      INNER JOIN image_group ig ON igc.image_group_id = ig.id
      AND igc.deleted = FALSE
    WHERE
      igc.deleted = FALSE
      AND jsonb_exists(igc.result, 'total_score_least_two_same')
      <if test="tag != null and tag != ''">
        AND jsonb_exists(ig.metadata -> 'tags', #{tag})
      </if>
  </select>
  <select id="selectAllBySingleUser" parameterType="map" resultMap="CaptionResultMap">
    SELECT
      id,
      caption,
      image_ids
    FROM (
      SELECT
        ig.image_ids,
        igcu.image_group_id AS id,
        igcu.caption,
        COUNT(*) OVER (PARTITION BY igcu.image_group_id) AS cnt,
        ROW_NUMBER() OVER (PARTITION BY igcu.image_group_id) AS rn
      FROM
        image_group_caption_user igcu
        INNER JOIN image_group ig ON igcu.image_group_id = ig.id
          AND igcu.deleted = FALSE
      WHERE
        igcu.deleted = FALSE
        <if test="tag != null and tag != ''">
          AND jsonb_exists(ig.metadata -> 'tags', #{tag})
        </if>
        AND igcu.user_id IN (
          SELECT id FROM "user" WHERE role = 'annotator'
        )
    ) t
    WHERE cnt = 1 AND rn = 1;
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from image_group
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from image_group
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupDO" useGeneratedKeys="true">
    insert into image_group (image_ids, metadata, create_time, modify_time,
      deleted)
    values (#{imageIds,jdbcType=OTHER}, #{metadata,jdbcType=OTHER}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP},
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupDO" useGeneratedKeys="true">
    insert into image_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="imageIds != null">
        image_ids,
      </if>
      <if test="metadata != null">
        metadata,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="imageIds != null">
        #{imageIds,jdbcType=OTHER},
      </if>
      <if test="metadata != null">
        #{metadata,jdbcType=OTHER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageGroupExample" resultType="java.lang.Long">
    select count(*) from image_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update image_group
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.imageIds != null">
        image_ids = #{record.imageIds,jdbcType=OTHER},
      </if>
      <if test="record.metadata != null">
        metadata = #{record.metadata,jdbcType=OTHER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update image_group
    set id = #{record.id,jdbcType=INTEGER},
      image_ids = #{record.imageIds,jdbcType=OTHER},
      metadata = #{record.metadata,jdbcType=OTHER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupDO">
    update image_group
    <set>
      <if test="imageIds != null">
        image_ids = #{imageIds,jdbcType=OTHER},
      </if>
      <if test="metadata != null">
        metadata = #{metadata,jdbcType=OTHER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupDO">
    update image_group
    set image_ids = #{imageIds,jdbcType=OTHER},
      metadata = #{metadata,jdbcType=OTHER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update image_group set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update image_group set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>