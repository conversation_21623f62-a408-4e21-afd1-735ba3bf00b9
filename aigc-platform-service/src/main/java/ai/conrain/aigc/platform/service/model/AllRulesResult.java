package ai.conrain.aigc.platform.service.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 所有规则查询结果封装类
 * 用于一次性返回样式白名单、功能白名单和构图黑名单的查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AllRulesResult {

    /**
     * 样式白名单规则
     */
    private Map<String, List<String>> styleWhitelistRules;

    /**
     * 功能白名单规则
     */
    private Map<String, List<String>> functionWhitelistRules;

    /**
     * 构图黑名单规则
     */
    private Map<String, List<String>> compositionBlacklistRules;

    /**
     * 检查样式白名单规则是否为空
     * 
     * @return 如果样式白名单规则为空或null返回true，否则返回false
     */
    public boolean isStyleWhitelistRulesEmpty() {
        return styleWhitelistRules == null || styleWhitelistRules.isEmpty();
    }

    /**
     * 检查功能白名单规则是否为空
     * 
     * @return 如果功能白名单规则为空或null返回true，否则返回false
     */
    public boolean isFunctionWhitelistRulesEmpty() {
        return functionWhitelistRules == null || functionWhitelistRules.isEmpty();
    }

    /**
     * 检查构图黑名单规则是否为空
     * 
     * @return 如果构图黑名单规则为空或null返回true，否则返回false
     */
    public boolean isCompositionBlacklistRulesEmpty() {
        return compositionBlacklistRules == null || compositionBlacklistRules.isEmpty();
    }
}