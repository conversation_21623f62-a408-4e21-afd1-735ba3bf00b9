package ai.conrain.aigc.platform.service.model.query;


import ai.conrain.aigc.platform.dal.pgsql.param.ImageBatchCountParam.MetadataFieldConfig;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ImageQuery
 *
 * @version ImageService.java v 0.1 2025-07-30 02:33:24
 */
@Data
public class ImageQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 图像ID，自增主键 */
    private Integer id;

    private List<Integer> ids;

    /** 图像类型，用于区分服装图、风格示意图 */
    private String type;

    /** 图像原始URL地址 */
    private String url;

    /** 展示图像URL地址 */
    private String showImgUrl;

    /** 图片路径 */
    private String imagePath;

    /** 图片内容哈希 */
    private String imageHash;

    /** 无图片内容哈希 */
    private Boolean noImageHash;

    /** 无图片大小信息 */
    private Boolean noImageSize;

    /** 图像元数据，JSON格式 */
    private JSONObject metadata;

    /** 图像元数据，查询字段值 */
    List<MetadataFieldConfig> metadataFieldConfigs;

    /** tags */
    private List<String> tags;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;
}
