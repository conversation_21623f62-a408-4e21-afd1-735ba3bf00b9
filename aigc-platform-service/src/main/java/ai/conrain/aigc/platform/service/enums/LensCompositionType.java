package ai.conrain.aigc.platform.service.enums;

/**
 * 镜头构图类型枚举
 * 定义不同的镜头构图类型及其对应的字符串键值
 */
public enum LensCompositionType {
    
    /**
     * 远景：主体显得很小，大量环境可见
     */
    LONG_SHOT("Long_shot"),
    
    /**
     * 全身：完整的主体从头到脚，最少的环境
     */
    FULL_BODY("Full_body"),
    
    /**
     * 半身：主体从大约胸部/肩部区域向上
     */
    BUST_SHOT("Bust_shot"),
    
    /**
     * 上半身特写：主体从腰部或肋骨区域向上
     */
    UPPER_BODY_CLOSE_UP("Upper_body_close_up"),
    
    /**
     * 下半身特写：专注于下半部分，通常是腿部和下躯干
     */
    LOWER_BODY_CLOSE_UP("Lower_body_close_up"),
    
    /**
     * 中景：主体从大约膝盖或大腿中部向上
     */
    MEDIUM_SHOT("Medium_shot");
    
    private final String key;
    
    LensCompositionType(String key) {
        this.key = key;
    }
    
    public String getKey() {
        return key;
    }
    
    /**
     * 根据key获取对应的枚举值
     * @param key 字符串键值
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static LensCompositionType getByKey(String key) {
        for (LensCompositionType type : values()) {
            if (type.getKey().equals(key)) {
                return type;
            }
        }
        return null;
    }
}