package ai.conrain.aigc.platform.service.enums;

/**
 * 规则类型枚举
 * 用于定义各种规则的键名常量，避免硬编码字符串
 *
 * <AUTHOR>
 */
public enum RuleType {
    /**
     * 参考图白名单
     */
    STYLE_WHITE_LIST("refWhiteList"),

    /**
     * 构图黑名单
     */
    COMPOSITION_BLACK_LIST("compositionBlackList"),

    /**
     * 用途白名单
     */
    USAGE_WHITE_LIST("usageWhiteList");

    private final String key;

    RuleType(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    @Override
    public String toString() {
        return key;
    }
}