package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.DistributorCustomerDAO;
import ai.conrain.aigc.platform.dal.entity.DistributorCustomerDO;
import ai.conrain.aigc.platform.dal.example.DistributorCustomerExample;
import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.OrganizationService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.UserProfileKeys;
import ai.conrain.aigc.platform.service.model.biz.RegisterCodeInfo;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.DistributorCustomerConverter;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.query.UserPointQuery;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorMerchantVO;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointVO;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * DistributorCustomerService实现
 *
 * <AUTHOR>
 * @version DistributorCustomerService.java v 0.1 2024-07-09 05:15:57
 */
@Slf4j
@Service
public class DistributorCustomerServiceImpl implements DistributorCustomerService {

    /** DAO */
    @Autowired
    private DistributorCustomerDAO distributorCustomerDAO;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserPointService userPointService;

    @Autowired
    private UserProfileService userProfileService;

    @Override
    public DistributorCustomerVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        DistributorCustomerDO data = distributorCustomerDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return DistributorCustomerConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = distributorCustomerDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除DistributorCustomer失败");
    }

    @Override
    public DistributorCustomerVO insert(DistributorCustomerVO distributorCustomer) {
        AssertUtil.assertNotNull(distributorCustomer, ResultCode.PARAM_INVALID, "distributorCustomer is null");
        AssertUtil.assertTrue(distributorCustomer.getId() == null, ResultCode.PARAM_INVALID,
            "distributorCustomer.id is present");

        //创建时间、修改时间兜底
        if (distributorCustomer.getCreateTime() == null) {
            distributorCustomer.setCreateTime(new Date());
        }

        if (distributorCustomer.getModifyTime() == null) {
            distributorCustomer.setModifyTime(new Date());
        }

        DistributorCustomerDO data = DistributorCustomerConverter.vo2DO(distributorCustomer);
        //逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = distributorCustomerDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建DistributorCustomer失败");
        AssertUtil.assertNotNull(data.getId(), "新建DistributorCustomer返回id为空");
        distributorCustomer.setId(data.getId());
        return distributorCustomer;
    }

    @Override
    public void updateByIdSelective(DistributorCustomerVO distributorCustomer) {
        AssertUtil.assertNotNull(distributorCustomer, ResultCode.PARAM_INVALID, "distributorCustomer is null");
        AssertUtil.assertTrue(distributorCustomer.getId() != null, ResultCode.PARAM_INVALID,
            "distributorCustomer.id is null");

        //修改时间必须更新
        distributorCustomer.setModifyTime(new Date());
        DistributorCustomerDO data = DistributorCustomerConverter.vo2DO(distributorCustomer);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = distributorCustomerDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新DistributorCustomer失败，影响行数:" + n);
    }

    /**
     * 删除渠道商的员工与客户的关联信息（销售、运营离职的情况）
     *
     * @param staffId
     */
    @Override
    public void onDeleteDistributorStaff(Integer staffId) {
        AssertUtil.assertNotNull(staffId, ResultCode.PARAM_INVALID, "staffId is null");

        //关联销售
        {
            DistributorCustomerQuery query = new DistributorCustomerQuery();
            query.setDistributorSalesUserId(staffId);
            List<DistributorCustomerVO> list = queryDistributorCustomerList(query);
            if (CollectionUtils.isNotEmpty(list)) {
                for (DistributorCustomerVO each : list) {
                    log.info("去除【销售】员工staffId:{}与渠道商客户customerId:{} 关联信息，DistributorCustomerVO.id:{}", staffId,each.getCustomerMasterUserId(), each.getId());

                    each.setDistributorSalesUserId(null);

                    DistributorCustomerDO obj = DistributorCustomerConverter.vo2DO(each);
                    obj.setDeleted(false);

                    //理论上不需要，保护一下
                    AssertUtil.assertNotNull(obj.getId(), "DistributorCustomerDO.id is null");

                    distributorCustomerDAO.updateByPrimaryKey(obj);
                }
            }
        }

        //关联运营
        {
            DistributorCustomerQuery query = new DistributorCustomerQuery();
            query.setDistributorOperatorUserId(staffId);
            List<DistributorCustomerVO> list = queryDistributorCustomerList(query);
            if (CollectionUtils.isNotEmpty(list)) {
                for (DistributorCustomerVO each : list) {
                    log.info("去除【运营】员工staffId:{}与渠道商客户customerId:{} 关联信息，DistributorCustomerVO.id:{}", staffId,each.getCustomerMasterUserId(), each.getId());

                    each.setDistributorOperatorUserId(null);

                    DistributorCustomerDO obj = DistributorCustomerConverter.vo2DO(each);
                    obj.setDeleted(false);

                    //理论上不需要，保护一下
                    AssertUtil.assertNotNull(obj.getId(), "DistributorCustomerDO.id is null");

                    distributorCustomerDAO.updateByPrimaryKey(obj);
                }
            }
        }
    }

    @Override
    public List<DistributorCustomerVO> queryDistributorCustomerList(DistributorCustomerQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        DistributorCustomerExample example = DistributorCustomerConverter.query2Example(query);

        List<DistributorCustomerDO> list = distributorCustomerDAO.selectByExample(example);
        return DistributorCustomerConverter.doList2VOList(list);
    }

    @Override
    public Long queryDistributorCustomerCount(DistributorCustomerQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        DistributorCustomerExample example = DistributorCustomerConverter.query2Example(query);
        long c = distributorCustomerDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询渠道商客户信息
     */
    @Override
    public PageInfo<DistributorCustomerVO> queryDistributorCustomerByPage(DistributorCustomerQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<DistributorCustomerVO> page = new PageInfo<>();

        DistributorCustomerExample example = DistributorCustomerConverter.query2Example(query);
        long totalCount = distributorCustomerDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<DistributorCustomerDO> list = distributorCustomerDAO.selectByExample(example);
        page.setList(DistributorCustomerConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public List<DistributorCustomerVO> queryDistributorCustomersByCurrentUserAuth(boolean includeCreatedCustomer) {

        DistributorCustomerExample exam = getDistributorCustomerExample(includeCreatedCustomer, null, null);

        List<DistributorCustomerDO> list = distributorCustomerDAO.selectByExample(exam);
        return DistributorCustomerConverter.doList2VOList(list);
    }

    /**
     * 查询当前用户权限下的渠道商简易客户信息列表，id/nickName
     *
     * @param includeCreatedCustomer
     * @return
     */
    @Override
    public List<UserVO> queryCustomerUsersOptionsByCurrentUserAuth(boolean includeCreatedCustomer) {

        DistributorCustomerExample exam = getDistributorCustomerExample(includeCreatedCustomer, null, null);

        List<DistributorCustomerDO> list = distributorCustomerDAO.selectByExample(exam);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return userService.batchQueryById(
            list.stream().map(DistributorCustomerDO::getCustomerMasterUserId).collect(Collectors.toList()));
    }

    /**
     * 获取查询条件
     * 渠道管理员看整个渠道的客户数据
     * 二级渠道商、运营组长、销售组长只能看各自及下属创建或关联的客户
     * 运营员工，只能看自己关联的客户
     * 销售员工，只能看自己关联的客户
     *
     * @return
     */
    private @NotNull DistributorCustomerExample getDistributorCustomerExample(boolean includeCreatedCustomer, Integer distributorOperatorUserId, Integer distributorSalesUserId) {
        DistributorCustomerExample exam = new DistributorCustomerExample();

        DistributorCustomerExample.Criteria c1 = exam.createCriteria();
        c1.andDistributorMasterUserIdEqualTo(OperationContextHolder.getMasterUserId());

        switch (Objects.requireNonNull(OperationContextHolder.getDistributorCustomRole())) {
            // 渠道商管理员和运营可以看到自己和下属的客户并可以根据 运营id和销售id 筛选
            case CHANNEL_ADMIN: {
            }
            // 渠道商运营可以看到自己和同级的客户并可以根据 运营id和销售id 筛选
            case OPS_MEMBER:
                if (Objects.nonNull(distributorOperatorUserId)) {
                    c1.andDistributorOperatorUserIdEqualTo(distributorOperatorUserId);
                }
                if (Objects.nonNull(distributorSalesUserId)) {
                    c1.andDistributorSalesUserIdEqualTo(distributorSalesUserId);
                }
                break;
            case SECOND_CHANNEL_ADMIN: {
                //二级渠道商看自己和下属的客户
                OrganizationVO deptOrg = organizationService.getSubOrganizationTreeByUserId(OperationContextHolder.getOperatorUserId());
                List<Integer> staffIds = organizationService.getUserIdsOfSubOrgIdsByOrgId(deptOrg.getId());

                c1.andDistributorSalesUserIdIn(staffIds);

                if (includeCreatedCustomer) {
                    DistributorCustomerExample.Criteria c2 = exam.createCriteria();
                    c2.andDistributorMasterUserIdEqualTo(OperationContextHolder.getMasterUserId());
                    c2.andCreatorIdIn(staffIds);

                    exam.or(c2);
                }
                break;
            }
            case SALES_MEMBER: {
                //销售只能看自己的客户
                c1.andDistributorSalesUserIdEqualTo(OperationContextHolder.getOperatorUserId());

                if (includeCreatedCustomer) {
                    DistributorCustomerExample.Criteria c2 = exam.createCriteria();
                    c2.andDistributorMasterUserIdEqualTo(OperationContextHolder.getMasterUserId());
                    c2.andCreatorIdEqualTo(OperationContextHolder.getOperatorUserId());

                    exam.or(c2);
                }
                break;
            }
            default:
                throw new RuntimeException("未知的角色:" + OperationContextHolder.getDistributorCustomRole());
        }
        return exam;
    }

    /**
     * 带条件分页查询渠道商客户信息
     */
    @Override
    public PageInfo<DistributorMerchantVO> queryDistributorMerchantByPage(DistributorCustomerQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<DistributorMerchantVO> page = new PageInfo<>();

        DistributorCustomerExample example = getDistributorCustomerExample(query.isIncludeCreatedCustomer(), query.getDistributorOperatorUserId(), query.getDistributorSalesUserId());

        for (DistributorCustomerExample.Criteria each : example.getOredCriteria()) {
            if (StringUtils.isNotBlank(query.getCustomerLike())) {
                each.andCustomerLike("'%"+query.getCustomerLike()+"%'");
            }
            if (query.isOnlyShow15DaysNotUsed()) {
                each.andNoVisitIn15Days();
            }
            if (query.isCustomerMusePointLessThan500()) {
                each.andCustomerMusePointLessThan500();
            }
            each.andLogicalDeleted(false);

            //推广注册码
            if (StringUtils.isNotBlank(query.getPromotionCode())) {
                List<Integer> userIdsByPromptionCode = userProfileService.queryUserIdsByPromptionCode(query.getPromotionCode());
                if (CollectionUtils.isNotEmpty(userIdsByPromptionCode)) {
                    each.andCustomerMasterUserIdIn(userIdsByPromptionCode);
                }
            }
        }

        //翻页参数
        example.page(query.getPageNum(), query.getPageSize());

        //排序参数
        if (StringUtils.isNotBlank(query.getOrderBy())) {
            example.setOrderByClause(query.getOrderBy());
        }

        long totalCount = distributorCustomerDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<DistributorCustomerDO> list = distributorCustomerDAO.selectByExample(example);
        List<UserVO> merchants = userService.batchQueryById(
            list.stream().map(DistributorCustomerDO::getCustomerMasterUserId).collect(Collectors.toList()));

        if (CollectionUtils.isNotEmpty(merchants) && query.isNeedCustomerImagePoint()) {
            UserPointQuery pointQuery = new UserPointQuery();
            pointQuery.setUserIdList(merchants.stream().map(UserVO::getId).collect(Collectors.toList()));
            List<UserPointVO> points = userPointService.queryUserPointList(pointQuery);

            if (CollectionUtils.isNotEmpty(points)) {

                // 填充用户积分
                merchants.forEach(userVO -> {
                    points.stream().filter(point -> point.getUserId().equals(userVO.getId())).findFirst().ifPresent(
                            userPointVO -> fillPoint(userVO, userPointVO));
                });
            }
        }

        Map<Integer, UserVO> userCache = new HashMap<>(
            merchants.stream().collect(Collectors.toMap(UserVO::getId, Function.identity())));

        //注意这里没有变更totalCount，后面如果想用这个关联，需要做sql join
        if (CollectionUtils.isNotEmpty(query.getStatusNotIn())) {
            list = list.stream().filter(
                e -> userCache.get(e.getCustomerMasterUserId()) != null && !query.getStatusNotIn()
                    .contains(userCache.get(e.getCustomerMasterUserId()).getStatus().getCode())).collect(
                Collectors.toList());
        }

        List<DistributorMerchantVO> ret = new ArrayList<>();

        //填充关联的渠道运营和销售人员信息
        fillDistStaffUserInfoVO(list, userCache, ret);

        // 填充profiles
        fillUserProfiles(ret);

        page.setList(ret);
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    //todo sql优化，加join
    private void fillDistStaffUserInfoVO(List<DistributorCustomerDO> list, Map<Integer, UserVO> userCache, List<DistributorMerchantVO> ret) {
        for (DistributorCustomerDO each : list) {
            UserVO merchantUser = userCache.get(each.getCustomerMasterUserId());
            AssertUtil.assertNotNull(merchantUser,
                "客户查询失败：customerMasterUserId:" + each.getCustomerMasterUserId());

            DistributorMerchantVO merchantVO = toDistributorMerchantVO(merchantUser);
            merchantVO.setDistributorMasterUserId(each.getDistributorMasterUserId());
            merchantVO.setDistributorOperatorUserId(each.getDistributorOperatorUserId());
            merchantVO.setDistributorSalesUserId(each.getDistributorSalesUserId());

            if (each.getDistributorOperatorUserId() != null) {
                UserVO user = getFromCacheOrFetch(each.getDistributorOperatorUserId(), userCache);
                if (user != null) {
                    merchantVO.setDistributorOperatorNickName(user.getNickName());
                    merchantVO.setDistributorOperatorMobile(user.getMobile());
                    userCache.put(user.getId(), user);
                }
            }

            if (each.getDistributorSalesUserId() != null) {
                UserVO user = getFromCacheOrFetch(each.getDistributorSalesUserId(), userCache);
                if (user != null) {
                    merchantVO.setDistributorSalesNickName(user.getNickName());
                    merchantVO.setDistributorSalesMobile(user.getMobile());
                    userCache.put(user.getId(), user);
                }
            }

            ret.add(merchantVO);
        }
    }

    private UserVO getFromCacheOrFetch(Integer uid, Map<Integer, UserVO> userCache) {
        if (userCache.containsKey(uid)) {
            return userCache.get(uid);
        }
        return userService.selectById(uid);
    }

    private void fillUserProfiles(List<DistributorMerchantVO> ret) {
        List<Integer> userIds = ret.stream().map(DistributorMerchantVO::getId).collect(Collectors.toList());
        UserProfileQuery userProfileQuery = new UserProfileQuery();
        userProfileQuery.setProfileKeys(Arrays.asList(CommonConstants.KEY_MEMO, UserProfileKeys.REGISTER_INVITE_CODE));
        userProfileQuery.setUids(userIds);
        List<UserProfileVO> userProfiles = userProfileService.queryUserProfileList(userProfileQuery);
        for (UserVO userVO : ret) {
            JSONObject valueByKey = new JSONObject();
            for (UserProfileVO profile : userProfiles) {
                if (profile.getUid().equals(userVO.getId())) {
                    valueByKey.put(profile.getProfileKey(), profile);

                    //推荐注册信息
                    if (StringUtils.equals(profile.getProfileKey(), UserProfileKeys.REGISTER_INVITE_CODE) && CommonUtil.isValidJson(profile.getProfileVal())) {
                        RegisterCodeInfo inviteCodeInfo = JSONObject.parseObject(profile.getProfileVal(), RegisterCodeInfo.class);
                        if (inviteCodeInfo != null && inviteCodeInfo.getDistributorCorpId() != null && inviteCodeInfo.getDistributorCorpId().equals(OperationContextHolder.getCorpOrgId())) {
                            userVO.setInviteRegister(String.format("%s(%s)", inviteCodeInfo.getNick(), inviteCodeInfo.getCode()));
                        }
                    }
                }
            }
            userVO.setProfiles(valueByKey);
        }
    }

    private static void fillPoint(UserVO user, UserPointVO point) {
        if (point == null) {
            return;
        }
        user.setImagePoint(point.getImagePoint());
        user.setDisplayGivePoint(point.getDisplayGivePoint());
        user.setExperiencePoint(point.getExperiencePoint());
    }

    private DistributorMerchantVO toDistributorMerchantVO(UserVO user) {
        DistributorMerchantVO distributorMerchantVO = new DistributorMerchantVO();
        BeanUtils.copyProperties(user, distributorMerchantVO);
        return distributorMerchantVO;
    }
}