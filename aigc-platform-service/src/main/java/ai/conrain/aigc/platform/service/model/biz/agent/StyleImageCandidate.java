package ai.conrain.aigc.platform.service.model.biz.agent;

import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import com.pgvector.PGvector;
import lombok.Data;

import java.util.Map;

@Data
public class StyleImageCandidate {
    private ImageCaptionVO imageCaption;
    private double styleSimilarity;
    private Map<String, PGvector> dimensionVectors;
    private double matchScore;
    public ClothShootGenreEnum getGenreEnum() {
        String s = getGenreStr();
        if (s == null) {
            return null;
        }
        return ClothShootGenreEnum.getByCode(s);
    }

    public String getGenreStr() {
        return imageCaption.getGenre();
    }
}
