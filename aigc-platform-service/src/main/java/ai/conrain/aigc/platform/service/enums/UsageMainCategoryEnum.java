package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

/**
 * 服装用途主分类枚举
 */
@Getter
public enum UsageMainCategoryEnum {
    FUNCTIONAL("Functional", "功能类"),
    OUTDOOR_WEAR("Outdoor_Wear", "户外"),
    HOME_WEAR("Home_Wear", "家居服"),
    FORMAL_WEAR("Formal_Wear", "正装"),
    UNDERWEAR("Underwear", "内衣"),
    SWIMWEAR("Swimwear", "泳装"),
    SPORTSWEAR("Sportswear", "运动服"),
    DAILY_USE("Daily_Use", "日常");

    private final String code;
    private final String desc;

    UsageMainCategoryEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UsageMainCategoryEnum getByCode(String code) {
        for (UsageMainCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }
}