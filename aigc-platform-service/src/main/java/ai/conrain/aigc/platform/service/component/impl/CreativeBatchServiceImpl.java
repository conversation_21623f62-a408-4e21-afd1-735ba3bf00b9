package ai.conrain.aigc.platform.service.component.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.dao.CreativeBatchDAO;
import ai.conrain.aigc.platform.dal.entity.CreativeBatchDO;
import ai.conrain.aigc.platform.dal.entity.StatsQueuedCreativeDO;
import ai.conrain.aigc.platform.dal.entity.StatsUserQueuedCreativeDO;
import ai.conrain.aigc.platform.dal.entity.UserCountDO;
import ai.conrain.aigc.platform.dal.example.CreativeBatchExample;
import ai.conrain.aigc.platform.dal.example.CreativeBatchExample.Criteria;
import ai.conrain.aigc.platform.integration.aliyun.AliyunTryonService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.aliyun.model.TryonTaskOutputModel;
import ai.conrain.aigc.platform.integration.kling.KlingTaskParams;
import ai.conrain.aigc.platform.integration.kling.KlingTaskRet;
import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.component.CreativeBatchElementsService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.creative.CreativeServiceFactory;
import ai.conrain.aigc.platform.service.component.dispatch.DispatchServiceFactory;
import ai.conrain.aigc.platform.service.component.dispatch.TaskDispatch;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums;
import ai.conrain.aigc.platform.service.enums.CreativeBizTypeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.PointLogTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.TaskStatusEnum;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.helper.KlingVideoHelper;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.helper.RemoveWrinkleTaskHelper;
import ai.conrain.aigc.platform.service.helper.WeakLockHelper;
import ai.conrain.aigc.platform.service.helper.WeakLockHelper.WeakType;
import ai.conrain.aigc.platform.service.model.biz.CommonParamsWrapper;
import ai.conrain.aigc.platform.service.model.biz.TryonRefinerTaskParams;
import ai.conrain.aigc.platform.service.model.biz.TryonTaskParams;
import ai.conrain.aigc.platform.service.model.biz.VideoClipGenReq;
import ai.conrain.aigc.platform.service.model.biz.VideoClipTask;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.common.UserCountVO;
import ai.conrain.aigc.platform.service.model.converter.CreativeBatchConverter;
import ai.conrain.aigc.platform.service.model.query.CommonTaskQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchElementsQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchElementsVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.LoraOption;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.FileUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.BizConstants.BIZ_TAG;
import static ai.conrain.aigc.platform.service.constants.BizConstants.DOWNLOADED_IMGS;
import static ai.conrain.aigc.platform.service.constants.BizConstants.EXAMPLE_IMAGES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.FINISH_TASK_COUNT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ASSIGN_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CREATIVE_QUERY_LASTED_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DELIVERY_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DELIVERY_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DEMO_TAG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DISLIKE_STATUS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_END_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_NEED_STORAGE_RESULT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LATEST_ZIP_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LIKE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LIKE_STATUS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_CLOTH_COLLOCATION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REFINE_STATUS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RELATED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RELATED_COMMON_TASK;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RELATED_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REQUEST_FREQUENCIES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REQUEST_FREQUENCY;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REQUEST_ORDER;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RESULT_IMG_URL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCHEDULE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_START_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TARGET_OSS_OBJECT_NAME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEMP_VIDEO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEMP_VIDEO_TASK;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TRANS_CLOTH_COLLOCATION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ZIP_URL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.ONE_DAY_SECONDS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.CREATIVE_VIP_USERS;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.SEE_ALL_MODELS_AND_HISTORY;
import static ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum.TRAINED_STATUS_LIST;

/**
 * CreativeBatchService实现
 *
 * <AUTHOR>
 * @version CreativeBatchService.java v 0.1 2024-05-08 03:35:56
 */
@Slf4j
@Service
public class CreativeBatchServiceImpl implements CreativeBatchService {
    private static final String LOCK_KEY_PREFIX = "_sync_batch_lock_";
    private static final int LOCK_EXPIRE_TIME = 5 * 60 * 1000;
    /**
     * DAO
     */
    @Autowired
    private CreativeBatchDAO creativeBatchDAO;
    @Autowired
    private UserService userService;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private CreativeBatchElementsService creativeBatchElementsService;
    @Autowired
    private UserPointService userPointService;
    @Lazy
    @Autowired
    private CreativeTaskService creativeTaskService;
    @Autowired
    private TairService tairService;
    @Lazy
    @Autowired
    private CreativeServiceFactory creativeService;
    @Lazy
    @Autowired
    private TaskDispatch creativeTaskDispatch;
    @Lazy
    @Autowired
    private DistributorCustomerService distributorCustomerService;
    @Autowired
    private WeakLockHelper weakLockHelper;
    @Autowired
    private OssService ossService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private KlingVideoHelper klingVideoHelper;

    @Autowired
    private CommonTaskService commonTaskService;

    @Autowired
    private AliyunTryonService aliyunTryonService;

    @Autowired
    private OssHelper ossHelper;

    private static final String VIDEO_CLIP_SUBMIT = "VIDEO_CLIP_SUBMIT";

    @Autowired
    private RemoveWrinkleTaskHelper removeWrinkleTaskHelper;
    @Autowired
    private DispatchServiceFactory dispatchServiceFactory;

    @Override
    public CreativeBatchVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CreativeBatchDO data = creativeBatchDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        CreativeBatchVO ret = CreativeBatchConverter.do2VO(data);

        fillElements(ret);
        fillVideoClipTaskStatus(ret);
        fillModelInfo(ret);
        fillNickName(Collections.singletonList(ret));

        return ret;
    }

    @Override
    public CreativeBatchVO getCreativeBatchByIdWithTask(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CreativeBatchDO data = creativeBatchDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        CreativeBatchVO ret = CreativeBatchConverter.do2VO(data);

        fillElements(ret, true);
        fillVideoClipTaskStatus(ret);
        fillModelInfo(ret);
        fillNickName(Collections.singletonList(ret));

        // 如果为 固定姿势创作/基础款换衣 需要进行额外处理
        if (ret.getType().equals(CreativeTypeEnum.FIXED_POSTURE_CREATION) || ret.getType().equals(
            CreativeTypeEnum.BASIC_CHANGING_CLOTHES)) {
            fillElementsByTask(ret);
        }

        return ret;
    }

    /**
     * 根据子任务进行数据填充
     *
     * @param ret 批次信息
     */
    private void fillElementsByTask(CreativeBatchVO ret) {
        List<CreativeBatchVO> batchList = batchQueryByIds(Collections.singletonList(ret.getId()), Boolean.TRUE);

        if (CollectionUtils.isEmpty(batchList) || batchList.get(0) == null) {
            return;
        }

        List<CreativeTaskVO> taskList = batchList.get(0).getCreativeTasksList();
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }

        // 提取所有的模特ID和场景ID
        Set<Integer> faceIds = extractElementIds(taskList, "FACE");
        Set<Integer> sceneIds = extractElementIds(taskList, "SCENE");

        // 查询模特数据
        List<CreativeElementVO> faceElements = CollectionUtils.isEmpty(faceIds) ? Collections.emptyList()
            : creativeElementService.batchQueryByIds(new ArrayList<>(faceIds));
        // 查询场景数据
        List<CreativeElementVO> sceneElements = CollectionUtils.isEmpty(sceneIds) ? Collections.emptyList()
            : creativeElementService.batchQueryByIds(new ArrayList<>(sceneIds));

        // 构建id->name映射，方便后续查找
        Map<Integer, String> faceIdNameMap = faceElements == null ? new HashMap<>() : faceElements.stream().filter(
            Objects::nonNull).collect(
            Collectors.toMap(CreativeElementVO::getId, CreativeElementVO::getName, (a, b) -> a));
        Map<Integer, String> sceneIdNameMap = sceneElements == null ? new HashMap<>() : sceneElements.stream().filter(
            Objects::nonNull).collect(
            Collectors.toMap(CreativeElementVO::getId, CreativeElementVO::getName, (a, b) -> a));

        // 批次整体的名称list
        List<String> faceNameList = new ArrayList<>(new HashSet<>(faceIdNameMap.values()));
        List<String> sceneNameList = new ArrayList<>(new HashSet<>(sceneIdNameMap.values()));
        ret.setFaceNameList(faceNameList);
        ret.setSceneNameList(sceneNameList);

        // 子任务分别设置自己的名称list
        taskList.forEach(task -> {
            Integer faceId = extractFaceIdFromTask(task);
            Integer sceneId = extractSceneIdFromTask(task);

            List<String> singleFaceNameList = faceId != null && faceIdNameMap.containsKey(faceId)
                ? Collections.singletonList(faceIdNameMap.get(faceId)) : Collections.emptyList();
            List<String> singleSceneNameList = sceneId != null && sceneIdNameMap.containsKey(sceneId)
                ? Collections.singletonList(sceneIdNameMap.get(sceneId)) : Collections.emptyList();

            task.setFaceNameList(singleFaceNameList);
            task.setSceneNameList(singleSceneNameList);
        });

        ret.setCreativeTasksList(taskList);
    }

    /**
     * 从任务列表中提取指定类型的元素ID
     *
     * @param taskList    任务列表
     * @param elementType 元素类型 (FACE/SCENE)
     * @return 元素ID集合
     */
    private Set<Integer> extractElementIds(List<CreativeTaskVO> taskList, String elementType) {
        //noinspection unchecked
        return taskList.stream().map(task -> {
            // 先判空，避免空指针
            if (task == null || task.getTplInfo() == null || task.getTplInfo().getTplParams() == null) {
                return null;
            }
            return task.getTplInfo().getTplParams();
        }).filter(Objects::nonNull).map(params -> params.get(elementType)).filter(Map.class::isInstance).map(
            obj -> (Map<String, Object>)obj).map(map -> map.get("id")).filter(Objects::nonNull).map(
            this::parseToInteger).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    /**
     * 安全地将对象转换为Integer
     *
     * @param obj 待转换的对象
     * @return 转换后的Integer，失败时返回null
     */
    private Integer parseToInteger(Object obj) {
        if (obj instanceof Integer) {
            return (Integer)obj;
        }
        if (obj instanceof String) {
            try {
                return Integer.parseInt((String)obj);
            } catch (NumberFormatException e) {
                log.warn("解析ID失败: {}", obj);
                return null;
            }
        }
        return null;
    }

    @Override
    public CreativeBatchVO getAndSync(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.BIZ_FAIL, "CreativeBatch不存在");

        // 如果当前记录未结束，则进行一次同步状态
        if (!data.getStatus().isEnd()) {
            try {
                syncStatus(data);
                log.info("状态同步成功，当前状态为{}", data.getStatus());
            } catch (Exception e) {
                log.error("状态同步失败，直接返回之前的数据batchId=" + id, e);
            }
        }

        return data;
    }

    @Override
    public List<CreativeBatchVO> batchQueryByIds(List<Integer> ids, Boolean isSelectTask) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        CreativeBatchExample example = new CreativeBatchExample();
        example.createCriteria().andIdIn(ids).andLogicalDeleted(false);

        List<CreativeBatchDO> list = creativeBatchDAO.selectByExampleWithBLOBs(example);

        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        List<CreativeBatchVO> result = CreativeBatchConverter.doList2VOList(list);
        fillElementInfo(result, isSelectTask);
        return result;
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = creativeBatchDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CreativeBatch失败");
    }

    @Override
    public <T extends CreativeRequest> CreativeBatchVO create(CreativeTypeEnum type, T request) throws IOException {
        return creativeService.create(type, request);
    }

    @Override
    public void updateOriginalImg4Video(Integer batchId, Integer index, String imageUrl) {
        AssertUtil.assertNotNull(batchId, ResultCode.PARAM_INVALID, "batchId is null");
        AssertUtil.assertNotNull(index, ResultCode.PARAM_INVALID, "index is null");
        AssertUtil.assertNotBlank(imageUrl, ResultCode.PARAM_INVALID, "imageUrl is null");

        CreativeBatchVO data = selectById(batchId);

        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "对应的图生视频批次不存在");
        AssertUtil.assertTrue(data.getType().isVideoCreative(), ResultCode.BIZ_FAIL, "目标任务不是图生视频任务");
        AssertUtil.assertTrue(!data.getStatus().isEnd(), ResultCode.BIZ_FAIL, "创作批次已完结");

        if (data.getExtInfo() == null || data.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE) == null) {
            log.warn("当前视频任务数据不完整，可能是脏数据ext={}", data.getExtInfo());
            throw new BizException(ResultCode.BIZ_FAIL);
        }

        // 更新信息
        String originImage = data.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE).getString(index);
        data.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE).set(index, imageUrl);
        data.getExtInfo().remove(KEY_TEMP_VIDEO + index);
        data.getExtInfo().remove(KEY_TEMP_VIDEO_TASK + index);

        log.info("updateOriginalImg4Video monitor,batchId={},index={},originImage={},updated to imageUrl={}", batchId,
            index, originImage, imageUrl);

        // 同时清空result images，防止影响展示（否则重置创作状态时会有展示问题）
        data.setResultImages(new ArrayList<>());

        if (data.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE).size() == 1) {
            data.setShowImage(imageUrl);
        }

        updateByIdSelective(data);
    }

    @Override
    public void updateByIdSelective(CreativeBatchVO creativeBatch) {
        AssertUtil.assertNotNull(creativeBatch, ResultCode.PARAM_INVALID, "creativeBatch is null");
        AssertUtil.assertTrue(creativeBatch.getId() != null, ResultCode.PARAM_INVALID, "creativeBatch.id is null");

        // 如果任务完结时，释放占用的服务和端口
        if (creativeBatch.getStatus() != null && creativeBatch.getStatus().isEnd()) {
            // 释放分发服务
            DispatchTypeEnum dispatchTypeEnum = DispatchTypeEnum.getByServerType(creativeBatch.getType());
            dispatchServiceFactory.release(dispatchTypeEnum, creativeBatch);

            //creativeTaskDispatch.release(creativeBatch);
        }

        // 修改时间必须更新
        creativeBatch.setModifyTime(new Date());
        CreativeBatchDO data = CreativeBatchConverter.vo2DO(creativeBatch);
        // 逻辑删除标过滤
        data.setDeleted(false);
        int n = creativeBatchDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新CreativeBatch失败，影响行数:" + n);
    }

    @Override
    public List<CreativeBatchVO> queryCreativeBatchList(CreativeBatchQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        CreativeBatchExample example = CreativeBatchConverter.query2Example(query);

        List<CreativeBatchDO> list = creativeBatchDAO.selectByExampleWithBLOBs(example);
        List<CreativeBatchVO> ret = CreativeBatchConverter.doList2VOList(list);
        if (CollectionUtils.isNotEmpty(ret)) {
            fillElements(ret);
        }

        return ret;
    }

    /**
     * 带条件分页查询创作批次
     */
    @Override
    public PageInfo<CreativeBatchVO> queryCreativeBatchByPage(CreativeBatchQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<CreativeBatchVO> page = new PageInfo<>();

        CreativeBatchExample example = CreativeBatchConverter.query2Example(query);
        long totalCount = creativeBatchDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<CreativeBatchDO> list = creativeBatchDAO.selectByExampleWithBLOBs(example);
        List<CreativeBatchVO> data = CreativeBatchConverter.doList2VOList(list);
        fillNickName(data);

        page.setList(data);
        if (CollectionUtils.isNotEmpty(page.getList())) {
            List<CreativeBatchVO> vos = page.getList();
            fillElements(vos);
            fillDeliveryInfo(vos);
            fillModelInfo(vos);
            page.setList(vos);
        }

        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Async
    @Override
    public void syncStatus(CreativeBatchVO data) {
        log.info("[syncStatus]异步执行状态同步开始,batchId={}, batchType={}", data.getId(), data.getType());
        if (!weakLockHelper.lock(WeakType.BATCH, data.getId())) {
            // 3秒内已执行过同步，直接返回，减少数据库压力
            log.info("3秒内任务已经进行过同步，直接返回当前数据，不需要同步状态,id={},uid={}", data.getId(),
                data.getOperatorId());
            return;
        }

        if (data.getType() == CreativeTypeEnum.CREATE_VIDEO) {
            log.info("【syncStatus】，进入视频创作流程={}={}", data.getId(), data.getType());

            // 视频任务，如果有请求外部视频创作api时，需要同步状态
            CreativeBatchVO target = selectById(data.getId());
            List<VideoClipTask> unCompletedVideoClipTasks = target.getUnCompletedVideoClipTasks();
            if (CollectionUtils.isNotEmpty(unCompletedVideoClipTasks)) {
                log.info("【syncStatus】当前视频创作中，有关联到外部api生成，需要同步状态batchId={}", target.getId());
                boolean needUpdate = false;
                for (VideoClipTask each : unCompletedVideoClipTasks) {
                    boolean eachUpdate = syncEachUnCompletedVideoClipTask(each, target);
                    needUpdate = eachUpdate || needUpdate;
                }
                if (needUpdate) {
                    this.updateByIdSelective(target);
                }
            }

            return;
        }

        // 衣服去皱（走咻图外部api发起的任务，不走comfyui，通过查询oss去判断任务状态）
        if (data.getType() == CreativeTypeEnum.REMOVE_WRINKLE
            || data.getType() == CreativeTypeEnum.REMOVE_WRINKLE_4_DC) {
            CreativeBatchVO targetBatch = selectById(data.getId());
            if (targetBatch != null && !targetBatch.getStatus().isEnd()) {
                syncStatus4RemoveWrinkleTask(targetBatch);
            }
            return;
        }

        if (data.getType().isManual()) {
            log.info("【syncStatus】当前批次id={}为手动任务type={}，无需调度，直接返回查询到的结果即可", data.getId(),
                data.getType());
            return;
        }

        log.info("【syncStatus】，id={},uid={},opid={},status={}", data.getId(), data.getUserId(), data.getOperatorId(),
            data.getStatus());

        long t1 = System.currentTimeMillis();

        String lockKey = LOCK_KEY_PREFIX + data.getId();
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);

        if (!lock) {
            log.info("任务正在处理中，直接返回当前数据，不需要同步状态,id={},uid={}", data.getId(), data.getOperatorId());
            return;
        }

        CreativeBatchVO target = selectById(data.getId());
        try {
            // 分布式锁之后，再查一遍数据库
            AssertUtil.assertNotNull(target, ResultCode.PARAM_INVALID, "data is null");
            if (target.getStatus().isEnd()) {
                log.info("任务已经结束，无需同步,id={},uid={}", target.getId(), target.getOperatorId());
                return;
            }

            List<CreativeTaskVO> taskList = null;
            if (!creativeTaskService.isExistsTaskByBatchId(data.getId())) {
                log.info("当前批次id={}任务未初始化完成，开始初始化任务", target.getId());
                taskList = creativeTaskService.initTask(target);
            }

            DispatchTypeEnum dispatchTypeEnum = DispatchTypeEnum.getByServerType(target.getType());
            String serverUrl = dispatchServiceFactory.dispatch(dispatchTypeEnum, target);

            if (StringUtils.isBlank(serverUrl)) {
                log.warn("当前可用服务为0，直接返回，operator={},batchId={}", target.getOperatorId(), target.getId());
                return;
            }

            if (CollectionUtils.isEmpty(taskList)) {
                taskList = creativeTaskService.queryTaskByBatchId(target.getId());
            }
            AssertUtil.assertTrue(CollectionUtils.isNotEmpty(taskList), ResultCode.BIZ_FAIL, "任务列表为空");

            List<CreativeTaskVO> unfinishedTasks = taskList.stream().filter(
                task -> task.getStatus() != CreativeStatusEnum.FINISHED).collect(Collectors.toList());

            // 过滤掉前置任务未完成的多阶段任务，前置任务完成之后会被放行
            creativeTaskService.filterMultiProcess(unfinishedTasks, Boolean.TRUE);

            target.setStatus(CreativeStatusEnum.PROCESSING);

            // 查询进行中的comfyui任务状态
            if (CollectionUtils.isNotEmpty(unfinishedTasks)) {
                Integer serverId = target.getExtValue(KEY_SERVER_ID, Integer.class);
                creativeTaskService.batchSyncStatus(unfinishedTasks, serverUrl, serverId);
            }

            AtomicInteger finishedCnt = new AtomicInteger(0);
            final BigDecimal[] taskSchedule = {BigDecimalUtils.newZero()};

            // 遍历任务列表，更新状态和进度
            taskList.forEach(task -> {

                if (task.getStatus() != CreativeStatusEnum.FINISHED) {
                    // 更新状态
                    target.setStatus(task.getStatus().getOrder() > target.getStatus().getOrder() ? task.getStatus()
                        : target.getStatus());

                    if (task.getStatus() == CreativeStatusEnum.PROCESSING) {
                        BigDecimal item = task.getExtInfo() != null ? task.getExtInfo().getBigDecimal(KEY_SCHEDULE)
                            : null;
                        item = item != null ? item : BigDecimalUtils.newZero();
                        taskSchedule[0] = BigDecimalUtils.greaterThan(item, taskSchedule[0]) ? item : taskSchedule[0];
                    }
                } else {
                    // 处理当前 task 是否需要添加进入 batch 中
                    reCheckTaskIsNeedStats(target, finishedCnt, task);
                }
            });

            if (target.getStatus() == CreativeStatusEnum.PROCESSING) {
                JSONObject json = new JSONObject();
                json.put("finished", finishedCnt);
                json.put("subSchedule", CommonConstants.CATTY_DECIMAL_FORMAT.format(taskSchedule[0]));
                target.addExtInfo(CommonConstants.KEY_SCHEDULE, json);

                if (StringUtils.isBlank(target.getExtValue(KEY_START_TIME, String.class))) {
                    target.addExtInfo(KEY_START_TIME, DateUtils.formatFullTime(new Date()));
                }
            }

            // 添加临时逻辑，如果是基础款换衣则需要根据其配置任务数量进行判断
            Integer finishTaskCount = target.getIntegerFromExtInfo(FINISH_TASK_COUNT);

            int finishThreshold = target.getType().equals(CreativeTypeEnum.BASIC_CHANGING_CLOTHES) ?
                target.getBatchCnt() * finishTaskCount : target.getBatchCnt();

            if (finishedCnt.get() == finishThreshold) {
                log.info("{}当前所有子任务都已经完成，设置状态为finished", target.getId());
                target.setStatus(CreativeStatusEnum.FINISHED);

                if (StringUtils.isBlank(target.getExtValue(KEY_END_TIME, String.class))) {
                    target.addExtInfo(KEY_END_TIME, DateUtils.formatFullTime(new Date()));
                }

                // 结束的时候将翻译结果回填到batch中，方便问题排查
                String trans = target.getExtValue(KEY_TRANS_CLOTH_COLLOCATION, String.class);
                if (StringUtils.isBlank(trans) && StringUtils.isNotBlank(
                    target.getExtValue(KEY_ORIGIN_CLOTH_COLLOCATION, String.class))) {

                    target.addExtInfo(KEY_TRANS_CLOTH_COLLOCATION,
                        taskList.get(0).getExtValue(KEY_TRANS_CLOTH_COLLOCATION, String.class));
                }
            }

            // 图片生成则更新本次创作的show image为结果图第一张图
            if (CollectionUtils.isNotEmpty(target.getResultImages()) && !target.getType().isVideoCreative()) {
                target.setShowImage(target.getResultImages().get(0));
            }
        } catch (Exception e) {
            log.error("syncStatus异常,batchId=" + data.getId(), e);
        } finally {

            if (target != null) {
                updateByIdSelective(target);

                if (target.getStatus() == CreativeStatusEnum.FAILED && userService.isVipWithoutBackUser(
                    target.getUserId())) {
                    log.error("[syncStatus]任务失败，请及时检查是否需要退点，batchId={},type={}", target.getId(),
                        target.getType());
                    DingTalkNoticeHelper.sendMsg2DevGroup(
                        "出图任务失败，请及时检查是否需要退点，batchId=" + target.getId() + ",type=" + target.getType(),
                        List.of("13675861364"));
                }
            }
            // 释放锁
            tairService.releaseLock(lockKey);

            long t2 = System.currentTimeMillis();
            log.info("syncStatus耗时：{} ms, id:{}", t2 - t1, data.getId());
        }
    }

    private void syncStatus4RemoveWrinkleTask(CreativeBatchVO targetBatch) {
        log.info("[syncStatus]羽绒服去皱任务batchId={}", targetBatch.getId());
        // 获取当前正在执行的 task
        Integer currentTaskId = targetBatch.getExtInfo(KEY_RELATED_COMMON_TASK, Integer.class);
        CommonTaskVO currentTask = commonTaskService.selectById(currentTaskId);
        AssertUtil.assertNotNull(currentTask, ResultCode.BIZ_FAIL,
            "[syncStatus]羽绒服去皱任务, 当前commonTask不存在, commonTaskId=" + currentTaskId);
        // taskOrder 从 0 开始
        Integer currentTaskOrder = currentTask.getTaskOrder();
        // 检查当前任务的状态
        if (!CommonTaskEnums.TaskStatus.COMPLETED.name().equals(currentTask.getOutTaskStatus())) {
            // 当前任务的状态为未完成, 检查出图结果
            String targetOssObjectName = currentTask.getStringFromExtInfo(KEY_TARGET_OSS_OBJECT_NAME);
            AssertUtil.assertNotBlank(targetOssObjectName, ResultCode.BIZ_FAIL,
                "[syncStatus]羽绒服去皱任务, 当前任务字段targetOssObjectName字段缺失, taskId=" + currentTaskId
                + ", taskOrder=" + currentTaskOrder);
            if (ossService.checkFileExists(targetOssObjectName)) {
                // 出图结果存在
                String retUrl = ossService.getSignedFileUrl(targetOssObjectName);
                AssertUtil.assertNotBlank(retUrl, ResultCode.BIZ_FAIL,
                    "[syncStatus]羽绒服去皱任务, 任务成功但获取URL失败, taskId=" + currentTaskId + ", taskOrder="
                    + currentTaskOrder);
                // 调用多次请求
                int requestFrequency = currentTask.getExtInfo(KEY_REQUEST_FREQUENCY, Integer.class);
                // requestOrder 从 0 开始
                int requestOrder = currentTask.getExtInfo(KEY_REQUEST_ORDER, Integer.class);
                // 留存中间结果
                currentTask.addExtInfo(KEY_RESULT_IMG_URL + "_" + requestOrder, retUrl);
                // 把最新的结果图保存到 retDetail.resultImgUrl
                currentTask.addRetDetail(KEY_RESULT_IMG_URL, retUrl);
                if (requestOrder < requestFrequency - 1) {
                    currentTask.addExtInfo(KEY_REQUEST_ORDER, requestOrder + 1);
                    // 把这一次的结果作为下一次的原图
                    currentTask.addExtInfo(KEY_ORIGIN_IMAGE, retUrl);
                    removeWrinkleTaskHelper.retryRemoveWrinkleTask(currentTask);
                } else {
                    // 当前 task 请求次数已达到指定数量, 更新状态为 COMPLETED, 并更新 batch 状态
                    // 更新common task
                    currentTask.setOutTaskStatus(CommonTaskEnums.TaskStatus.COMPLETED.name());
                    currentTask.setTaskStatus(CommonTaskEnums.TaskStatus.COMPLETED.name());
                    currentTask.setTaskEndTime(new Date());
                    commonTaskService.updateByIdSelective(currentTask);
                    // 更新targetBatch
                    if (currentTaskOrder == 0) {
                        targetBatch.setShowImage(retUrl);
                    }
                    targetBatch.addResultImage(retUrl); // 存入结果图
                    if (!(currentTaskOrder < targetBatch.getBatchCnt() - 1)) {
                        // 如果 batch 已经可以结束, 直接结束
                        targetBatch.addExtInfo(KEY_END_TIME, DateUtils.formatFullTime(new Date()));
                        targetBatch.setStatus(CreativeStatusEnum.FINISHED);
                    }
                }
            } else {
                // 出图结果不存在
                Date creativeTime = currentTask.getCreateTime();
                if (DateUtils.diffNowSecond(creativeTime) > 180) {
                    // 当前任务超时, 重试原任务
                    removeWrinkleTaskHelper.retryRemoveWrinkleTask(currentTask);
                }
                targetBatch.setStatus(CreativeStatusEnum.PROCESSING);
            }
        } else {
            // 当前任务状态已完成
            if (currentTaskOrder < targetBatch.getBatchCnt() - 1) {
                // 任务数量未达成, 继续创建新的任务
                // 获取当前任务的结果图, 作为新任务的原图
                String originImgUrl = currentTask.getRetDetail(KEY_RESULT_IMG_URL, String.class);
                AssertUtil.assertNotBlank(originImgUrl, ResultCode.BIZ_FAIL, "[syncStatus]羽绒服去皱任务原始图片为空");
                int newTaskOrder = currentTaskOrder + 1;
                List<Integer> requestFrequencies = targetBatch.getExtInfoList(KEY_REQUEST_FREQUENCIES, Integer.class);
                CommonTaskVO newTask = removeWrinkleTaskHelper.createRemoveWrinkleTask(originImgUrl, targetBatch,
                    newTaskOrder, requestFrequencies.get(newTaskOrder));
                targetBatch.addExtInfo(KEY_RELATED_COMMON_TASK, newTask.getId());
                targetBatch.setStatus(CreativeStatusEnum.PROCESSING);
            } else {
                // 任务数量已达到, targetBatch 结束
                targetBatch.setStatus(CreativeStatusEnum.FINISHED);
                targetBatch.addExtInfo(KEY_END_TIME, DateUtils.formatFullTime(new Date()));
            }
        }
        this.updateByIdSelective(targetBatch);
    }

    /**
     * 同步视频剪辑任务状态
     *
     * @param clipTaskView 视频剪辑任务
     * @param targetBatch  目标batch
     * @return true, 需要更新
     */
    private boolean syncEachUnCompletedVideoClipTask(VideoClipTask clipTaskView, CreativeBatchVO targetBatch) {

        if (clipTaskView.getTaskId() == null) {
            log.warn("clipTaskView.taskId为空，预期之外的脏数据，忽略:{}", clipTaskView);
            return false;
        }

        CommonTaskVO task = commonTaskService.lockById(clipTaskView.getTaskId());
        if (task == null) {
            log.error("当前task不存在，忽略，common_task id={}", clipTaskView.getTaskId());
            return false;
        }

        // 外部任务id
        String outTaskId = task.getOutTaskId();

        // 任务待提交
        if (StringUtils.isBlank(outTaskId)) {

            // 控频次，3秒内只提交一个创建任务
            boolean canSubmit = tairService.acquireLock(VIDEO_CLIP_SUBMIT, 100);
            if (canSubmit) {
                // 请求可灵生成任务
                KlingTaskParams klingTaskParams = new KlingTaskParams();
                klingTaskParams.setPrompt(clipTaskView.getPrompt().trim());
                klingTaskParams.setDuration(clipTaskView.getDuration());

                String originalImageUrl = getOriginalImgUrlByIndex(targetBatch, clipTaskView.getIndex());
                AssertUtil.assertNotBlank(originalImageUrl, ResultCode.PARAM_INVALID, "视频片段原图地址不能为空");
                klingTaskParams.setImageUrl(originalImageUrl);

                String klingTaskId = null;
                try {
                    klingTaskId = klingVideoHelper.createVideoTask(klingTaskParams);
                } catch (Exception e) {
                    log.error("[syncStatus]创建视频任务失败", e);
                }

                // 提交创建失败
                if (StringUtils.isBlank(klingTaskId)) {

                    tairService.releaseLock(VIDEO_CLIP_SUBMIT);

                    log.error("创建视频任务失败，等下次重试，commonTask.id={}", task.getId());

                    DingTalkNoticeHelper.sendMsg2DevGroup(
                        "创建视频任务失败，等下次重试\n任务batchId=" + targetBatch.getId() + "\ntraceId=" + MDC.get(
                            "traceId"));

                    // 提交创建成功
                } else {

                    clipTaskView.setOutTaskId(klingTaskId);
                    clipTaskView.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.KLING.name());

                    // 更新batch
                    targetBatch.addExtInfo(KEY_TEMP_VIDEO_TASK + clipTaskView.getIndex(), clipTaskView);

                    // 更新common task
                    task.setReqBizParams(CommonParamsWrapper.wrap(klingTaskParams));
                    task.setOutTaskId(klingTaskId);
                    task.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.KLING.name());
                    commonTaskService.updateByIdSelective(task);
                    return true;
                }

            } else {
                log.info("需要提交视频切片任务，但没请求到锁，等下次调度 CommonTask.id:{}", clipTaskView.getTaskId());
            }

            // 已经有任务，且任务状态没有结束时，查询任务状态
        } else if (!CommonTaskEnums.TaskStatus.isEnd(task.getTaskStatus()) || !CommonTaskEnums.TaskStatus.isEnd(
            clipTaskView.getTaskStatus())) {
            KlingTaskRet klingTaskRet = klingVideoHelper.getVideoTask(outTaskId);

            // 查询失败（网络超时的情况）
            if (klingTaskRet == null || klingTaskRet.getStatus() == null) {
                log.info("查询视频结果失败，下次查询再重试。返回：{}", klingTaskRet);

                // 查询到结果，再看是否生成成功
            } else {
                String taskStatus = CommonTaskEnums.TaskStatus.fromKlingTaskStatus(klingTaskRet.getStatus()).name();
                clipTaskView.setTaskStatus(taskStatus);

                // 视频生成成功
                if (StringUtils.isNotBlank(klingTaskRet.getOutVideoUrl()) && StringUtils.isNotBlank(
                    klingTaskRet.getOssVideoUrl())) {

                    clipTaskView.setOutVideoUrl(klingTaskRet.getOutVideoUrl());

                    // 更新oss url
                    targetBatch.addExtInfo(KEY_TEMP_VIDEO + clipTaskView.getIndex(), klingTaskRet.getOssVideoUrl());
                    clipTaskView.setOssVideoUrl(klingTaskRet.getOssVideoUrl());
                }

                // 更新batch
                targetBatch.addExtInfo(KEY_TEMP_VIDEO_TASK + clipTaskView.getIndex(), clipTaskView);

                // 更新common task
                task.setTaskStatus(taskStatus);
                task.setOutTaskStatus(klingTaskRet.getStatus().name());
                if (task.getTaskStartTime() == null) {
                    task.setTaskStartTime(new Date());
                }
                if (CommonTaskEnums.TaskStatus.isEnd(task.getTaskStatus())) {
                    task.setTaskEndTime(new Date());
                }
                task.setRetDetail(JSONObject.toJSONString(klingTaskRet));

                commonTaskService.updateByIdSelective(task);

                // 视频任务失败情况，（重试，或通知人工处理）
                if (StringUtils.equals(taskStatus, CommonTaskEnums.TaskStatus.FAILED.name())) {
                    CommonTaskQuery ctq = new CommonTaskQuery();
                    ctq.setRelatedBizType(CommonTaskEnums.RelatedBizType.CREATE_VIDEO_CLIP.getCode());
                    ctq.setRelatedBizId(clipTaskView.getId() + "_" + clipTaskView.getIndex());
                    List<CommonTaskVO> list = commonTaskService.queryCommonTaskList(ctq);
                    if (list == null || list.size() <= 10) {
                        log.warn("视频任务失败，进行自动重试，batchId:{}, index:{}", clipTaskView.getId(),
                            clipTaskView.getIndex());

                        DingTalkNoticeHelper.sendMsg2DevGroup(
                            "视频任务失败，系统自动重试\n重试次数=" + (list == null ? 1 : list.size()) + "\ntraceId="
                            + MDC.get("traceId"));

                        VideoClipGenReq retry = new VideoClipGenReq();
                        retry.setId(clipTaskView.getId());
                        retry.setIndex(clipTaskView.getIndex());
                        retry.setPrompt(clipTaskView.getPrompt());
                        retry.setDuration(clipTaskView.getDuration());

                        this.apply2GenVideoClip(retry);

                    } else {
                        log.error("视频任务失败，尝试{}次后仍然失败，需要页面手工处理，返回：{}", list.size(),
                            klingTaskRet);
                        DingTalkNoticeHelper.sendMsg2DevGroup(
                            "视频任务失败，需要页面上重试\ntraceId=" + MDC.get("traceId"));
                    }
                }

                return true;
            }
        } else {
            log.info("当前视频切片任务需要人工处理，状态:{}，Batch.id:{}, CommonTask.id:{}", task.getTaskStatus(),
                clipTaskView.getId(), clipTaskView.getTaskId());
        }

        return false;
    }

    private String getOriginalImgUrlByIndex(CreativeBatchVO batch, Integer index) {
        if (batch.getExtInfo() != null && batch.getExtInfo().containsKey(KEY_ORIGIN_IMAGE)
            && batch.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE) != null && batch.getExtInfo().getJSONArray(
            KEY_ORIGIN_IMAGE).size() > index) {

            return batch.getExtInfo().getJSONArray(KEY_ORIGIN_IMAGE).getString(index);
        }
        return null;
    }

    @Override
    public List<CreativeBatchVO> queryActive(List<String> types, ModelTypeEnum modelType) {
        CreativeBatchExample example = new CreativeBatchExample();
        Criteria criteria = example.createCriteria();
        criteria.andOperatorIdEqualTo(OperationContextHolder.getOperatorUserId()).andStatusIn(
            CreativeStatusEnum.getUncompleteStatusList()).andDeletedEqualTo(false).andTypeIn(types);
        if (modelType != null) {
            criteria.andModelTypeEqualTo(modelType.getCode());
        }
        // 正序，取最早的那个任务
        example.setOrderByClause("id");

        List<CreativeBatchDO> list = creativeBatchDAO.selectByExampleWithBLOBs(example);
        return CreativeBatchConverter.doList2VOList(list);
    }

    /**
     * 查询今天的创作批次列表
     *
     * @param types     创作类型列表，筛选特定类型的创作批次
     * @param modelType 模型类型，筛选特定模型类型的创作批次
     * @param isSelect  是否查询任务列表，默认不查询
     * @return 符合条件的创作批次VO列表
     */
    @Override
    public List<CreativeBatchVO> queryTodayList(List<String> types, ModelTypeEnum modelType, boolean isSelect,
                                                CreativeBizTypeEnum bizType) {
        // 1、构建查询条件  设置基础查询条件：未删除且类型在指定列表中
        CreativeBatchExample example = new CreativeBatchExample();
        Criteria criteria = example.createCriteria().andDeletedEqualTo(false).andTypeIn(types);

        if (bizType != null && bizType != CreativeBizTypeEnum.ALL) {
            criteria.andBizTypeEqualTo(bizType.getCode());
        }

        // 2、根据角色设置不同的查询条件
        // 普通商家和运营角色
        if (!OperationContextHolder.isDistributorRole()) {
            criteria.andOperatorIdOrBizTagEqualTo(OperationContextHolder.getOperatorUserId(), EXAMPLE_IMAGES);
            // 渠道商角色
        } else {
            criteria.andOperatorIdEqualTo(OperationContextHolder.getOperatorUserId());
        }

        // 3、非后台角色且无特殊权限时的模型权限过滤
        if (!OperationContextHolder.isBackRole() && !systemConfigService.isInJsonArray(SEE_ALL_MODELS_AND_HISTORY,
            OperationContextHolder.getOperatorUserId())) {
            if (OperationContextHolder.isDistributorRole()) {
                criteria.andEnabledModel();
            } else {
                criteria.andEnabledModel(OperationContextHolder.getMasterUserId());
            }
        }

        // 4、设置用户ID过滤，只查询当前主账号的数据
        criteria.andUserIdEqualTo(OperationContextHolder.getMasterUserId());

        // 5、设置模型类型过滤(如果指定)
        if (modelType != null) {
            criteria.andModelTypeEqualTo(modelType.getCode());
        }

        // 6、设置时间范围过滤(昨天之后的数据)
        criteria.andCreateTimeGreaterThan(DateUtils.getYesterday());

        // 7、增量查询处理：如果有上次查询时间，则只查询该时间之后的修改数据
        Long lastedTime = tairService.getObject(getQueryLastedTimeKey(types, modelType), Long.class);
        if (lastedTime != null) {
            criteria.andModifyTimeGreaterThanOrEqualTo(new Date(lastedTime));
        }

        // 8、设置排序(按ID倒序)
        example.setOrderByClause("id desc");

        // 9、执行查询
        List<CreativeBatchDO> list = creativeBatchDAO.selectByExampleWithBLOBs(example);

        // 10、 转换结果并填充额外信息
        List<CreativeBatchVO> result = CreativeBatchConverter.doList2VOList(list);
        fillElementInfo(result, isSelect);
        fillModelInfo(result);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Integer id) {
        CreativeBatchVO data = selectById(id);
        if (data == null) {
            log.info("取消任务时发现任务不存在，直接跳过，id:{}", id);
            return;
        }

        AssertUtil.assertOperatePermission(data.getUserId());

        AssertUtil.assertTrue(data.getStatus() == CreativeStatusEnum.QUEUE, ResultCode.NOT_QUEUE_CANNOT_CANCEL,
            "当前批次任务正在处理中，不允许取消" + id);

        String lockKey = LOCK_KEY_PREFIX + data.getId();
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);

        if (!lock) {
            log.error("任务正在处理中，无法取消,id={},uid={}", data.getId(), data.getOperatorId());
            throw new BizException(ResultCode.NOT_QUEUE_CANNOT_CANCEL, "当前批次任务正在处理中，不允许取消" + id);
        }

        try {
            CreativeBatchDO batch = creativeBatchDAO.lockByPrimaryKey(data.getId());
            AssertUtil.assertNotNull(batch, ResultCode.BIZ_FAIL, "任务不存在" + id);

            // 执行删除动作
            deleteById(id);

            List<CreativeTaskVO> tasks = creativeTaskService.queryTaskByBatchId(id);
            for (CreativeTaskVO each : tasks) {
                AssertUtil.assertTrue(each.getStatus() == CreativeStatusEnum.INIT, ResultCode.NOT_QUEUE_CANNOT_CANCEL,
                    "当前任务正在处理中，不允许取消" + id);
                creativeTaskService.deleteById(each.getId());
            }

            // 恢复用户点数(手部修复暂时免费)
            if (BigDecimalUtils.greaterThanZero(data.getType().getConsumeMusePoints())
                && !OperationContextHolder.isDistributorRole() && !OperationContextHolder.isAdmin()) {
                userPointService.revertByImage(data);
            }
        } finally {
            tairService.releaseLock(lockKey);
        }
    }

    @Override
    public void clear(List<String> types, ModelTypeEnum modelType) {
        // 增加缓存
        tairService.setObject(getQueryLastedTimeKey(types, modelType), System.currentTimeMillis(), ONE_DAY_SECONDS);
    }

    /**
     * 查询当前登录用户所有有创作历史的模型(创作记录的服装下拉菜单)
     */
    @Override
    public List<LoraOption> queryModels4HistoryTasks() {
        MaterialModelQuery query = new MaterialModelQuery();
        query.setOrderBy("id desc");

        switch (OperationContextHolder.getRoleType()) {
            // 管理员和运营可以看全部服装
            case ADMIN:
                break;
            case OPERATOR:
                break;

            // 渠道商要进一步分角色（渠道管理，创作记录-服装下拉菜单）
            // 管理员/二级管理员，可以看到自己和下级和会员的所有服装
            case DISTRIBUTOR: {
                List<DistributorCustomerVO> customers
                    = distributorCustomerService.queryDistributorCustomersByCurrentUserAuth(true);
                if (CollectionUtils.isEmpty(customers)) {
                    return new ArrayList<>();
                }

                query.setUserIds(customers.stream().map(DistributorCustomerVO::getCustomerMasterUserId)
                    .collect(Collectors.toList()));
                break;
            }
            // 商家只能看自己的
            case MERCHANT: {
                query.setUserId(OperationContextHolder.getOperatorUserId());
                break;
            }
        }

        query.setMaterialType(MaterialType.cloth.name());
        if (OperationContextHolder.isBackRole()) {
            query.setStatusList(TRAINED_STATUS_LIST);
        } else {
            query.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
        }

        query.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);

        List<MaterialModelVO> models = materialModelService.queryMaterialModelList(query);
        if (CollectionUtils.isNotEmpty(models)) {
            return models.stream().map(m -> {
                LoraOption option = new LoraOption();
                option.setId(m.getId());
                option.setName(m.getName());
                return option;
            }).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void imageLike(Integer taskId, Integer batchId, Boolean like, String imageUrl) {
        CreativeTaskVO task = creativeTaskService.selectById(taskId);

        boolean isMultiImage = task != null && task.getBatchCnt() > 1 && StringUtils.isNotBlank(imageUrl);

        if (OperationContextHolder.isBackRole() && task != null && userService.isCustomer(task.getUserId())) {
            log.info("后台用户不能对前台用户的图片进行点赞/踩，currentUserId={}，直接跳过,batchId={}",
                OperationContextHolder.getOperatorUserId(), task.getBatchId());
            return;
        }

        String likeStatus = like ? KEY_LIKE_STATUS : KEY_DISLIKE_STATUS;

        // 如果是精选图时，可能taskId是不存在的
        if (task != null) {
            CreativeTaskVO target = new CreativeTaskVO();
            target.setId(taskId);
            target.setExtInfo(task.getExtInfo());

            if (isMultiImage) {
                JSONObject likeObj = target.getExtValue(KEY_LIKE, JSONObject.class);
                if (likeObj == null) {
                    likeObj = new JSONObject();
                }
                likeObj.put(imageUrl, likeStatus);
                target.addExtInfo(KEY_LIKE, likeObj);
            } else {
                target.addExtInfo(KEY_LIKE, likeStatus);
            }
            creativeTaskService.updateByIdSelective(target);

            if (batchId == null) {
                batchId = task.getBatchId();
            }
        }

        if (batchId == null) {
            log.error("taskId={}，点赞/踩异常{}，batchId为空", taskId, like);
            return;
        }

        // 同时更新batch中的状态，减少前端查询压力
        CreativeBatchVO batch = selectById(batchId);
        CreativeBatchVO target = new CreativeBatchVO();
        target.setId(batchId);
        target.setExtInfo(batch.getExtInfo());

        JSONObject likeObj = target.getExtValue(KEY_LIKE, JSONObject.class);
        if (likeObj == null) {
            likeObj = new JSONObject();
            target.getExtInfo().put(KEY_LIKE, likeObj);
        }

        String key = isMultiImage ? imageUrl : String.valueOf(taskId);
        likeObj.put(key, likeStatus);
        updateByIdSelective(target);
    }

    @Override
    public List<CreativeBatchVO> queryUnCompletedTopUser(Integer pipelineId, Integer limit) {
        return queryUnCompletedTopUser(pipelineId, limit, null, null, true);
    }

    @Override
    public List<CreativeBatchVO> queryUnProcessedTopUser(Integer pipelineId, Integer limit) {
        return queryUnCompletedTopUser(pipelineId, limit, null, null, false);
    }

    @Override
    public List<CreativeBatchVO> queryUnProcessedTopUser(Integer pipelineId, List<String> exceptTypeList,
                                                         List<String> includeTypeList, Integer limit) {
        return queryUnCompletedTopUser(pipelineId, limit, exceptTypeList, includeTypeList, false);
    }

    @Override
    public List<CreativeBatchVO> queryUnCompletedTopUser(Integer pipelineId, List<String> exceptTypeList,
                                                         List<String> includeTypeList, Integer limit) {
        return queryUnCompletedTopUser(pipelineId, limit, exceptTypeList, includeTypeList, true);
    }

    @Override
    public CreativeBatchVO insert(CreativeBatchVO data) {
        CreativeBatchDO target = CreativeBatchConverter.vo2DO(data);
        target.setCreateTime(new Date());
        target.setDeleted(false);
        int cnt = creativeBatchDAO.insertSelective(target);
        AssertUtil.assertTrue(cnt == 1 && target.getId() != null, ResultCode.BIZ_FAIL, "创建创作批次失败");
        data.setId(target.getId());
        return data;
    }

    @Override
    public void changeExampleImages(Integer modelId, Integer userId, List<String> exampleImages) {
        AssertUtil.assertNotNull(modelId, ResultCode.PARAM_INVALID, "modelId is null");
        AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(exampleImages), ResultCode.PARAM_INVALID,
            "exampleImages is null");

        CreativeBatchVO data = queryExampleImagesItem(modelId);
        if (data == null) {
            data = CreativeBatchConverter.buildExampleImagesVO(modelId, userId, exampleImages);
            insert(data);
            return;
        }

        data.setResultImages(exampleImages);
        data.setShowImage(exampleImages.get(0));
        data.setBatchCnt(exampleImages.size());
        updateByIdSelective(data);
    }

    @Override
    public void clearExampleImages(Integer modelId, Integer userId) {
        CreativeBatchExample example = new CreativeBatchExample();
        example.createCriteria().andModelIdEqualTo(modelId).andUserIdEqualTo(userId).andBizTagEqualTo(EXAMPLE_IMAGES);
        creativeBatchDAO.logicalDeleteByExample(example);
    }

    @Override
    public List<String> queryExampleImages(Integer modelId) {
        CreativeBatchVO data = queryExampleImagesItem(modelId);

        if (data == null) {
            return null;
        }
        return data.getResultImages();
    }

    @Override
    public void assignExampleImages(Integer modelId, Integer userId) {
        CreativeBatchVO data = queryExampleImagesItem(modelId);
        if (data == null) {
            return;
        }

        data.setUserId(userId);
        data.setOperatorId(userId);

        updateByIdSelective(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setToFail(Integer id,String needRefundMusePoint) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(!data.getStatus().isEnd(), ResultCode.BIZ_FAIL, "创作批次状态错误" + data.getStatus());

        String lockKey = LOCK_KEY_PREFIX + data.getId();
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);

        if (!lock) {
            log.error("任务正在处理中，无法设置为失败,id={},uid={}", data.getId(), data.getOperatorId());
            throw new BizException(ResultCode.NOT_QUEUE_CANNOT_CANCEL, "当前批次任务正在处理中，无法设置为失败" + id);
        }

        try {
            // 如果需要退点
            if (StringUtils.equals(needRefundMusePoint, YES)) {
                // 检查是否已经退过点，通过退点标记,已经退点的直接抛出异常
                AssertUtil.assertTrue(!StringUtils.equals(data.getExtInfo().getString(CommonConstants.needRefundMusePoint),CommonConstants.YES),ResultCode.MODEL_ALREADY_REFUNDED, "该视频已完成退点，无法重复操作");
                // 下面是需要退点，还没退点过的情况
                if (data.getType().getLogType() == PointLogTypeEnum.CREATE_VIDEO_CREATE) {
                    userPointService.revertByVideo(data);
                }else {
                    userPointService.revertByImage(data);
                }
                // 退点后打上退点成功标记
                JSONObject extInfo = data.getExtInfo();
                extInfo.put(CommonConstants.needRefundMusePoint,CommonConstants.YES);
                data.setExtInfo(extInfo);
            }
            data.setStatus(CreativeStatusEnum.FAILED);
            updateByIdSelective(data);

            // 设置关联的任务状态为失败
            List<CreativeTaskVO> tasks = creativeTaskService.queryTaskByBatchId(id);
            tasks.forEach(task -> {
                task.setStatus(CreativeStatusEnum.FAILED);
                creativeTaskService.updateByIdSelective(task);
            });
        } finally {
            tairService.releaseLock(lockKey);
        }
    }

    @Override
    public void uploadVideo(Integer id, List<String> videos) {
        CreativeBatchVO data = selectById(id);

        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(data.getType() == CreativeTypeEnum.CREATE_VIDEO, ResultCode.BIZ_FAIL,
            "创作类型错误" + data.getType());

        // 添加前，先清理历史的数据
        if (CollectionUtils.isNotEmpty(data.getResultImages())) {
            data.getResultImages().clear();
        }

        data.addResultImage(videos);
        data.setStatus(CreativeStatusEnum.FINISHED);

        if (StringUtils.isBlank(data.getExtInfo(KEY_DELIVERY_TIME, String.class))) {
            data.addExtInfo(KEY_DELIVERY_TIME, DateUtils.formatTime(new Date()));
            data.addExtInfo(KEY_DELIVERY_OPERATOR, OperationContextHolder.getOperatorUserId());
        }

        updateByIdSelective(data);
    }

    @Override
    public void removeFixFace(Integer id, Integer index) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(!data.getStatus().isEnd(), ResultCode.BIZ_FAIL, "创作批次已完结");

        data.getExtInfo().remove(KEY_RELATED + index);
        updateByIdSelective(data);
    }

    @Override
    public void assignVideoOperator(Integer id, String mobile) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(data.getType() == CreativeTypeEnum.CREATE_VIDEO, ResultCode.BIZ_FAIL,
            "创作类型错误" + data.getType());

        data.addExtInfo(KEY_RELATED_OPERATOR, mobile);
        updateByIdSelective(data);
    }

    @Override
    public long queryCount(CreativeBatchQuery query) {
        CreativeBatchExample example = CreativeBatchConverter.query2Example(query);
        return creativeBatchDAO.countByExample(example);
    }

    @Override
    public void applyRefine(Integer id) {
        CreativeBatchDO data = creativeBatchDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        CreativeBatchVO target = CreativeBatchConverter.do2VO(data);

        String bizTag = target.getExtValue(BIZ_TAG, String.class);
        AssertUtil.assertTrue(StringUtils.equals(bizTag, EXAMPLE_IMAGES), ResultCode.BIZ_FAIL, "当前批次非精选图");

        TaskStatusEnum taskStatus = target.getExtValue(KEY_REFINE_STATUS, TaskStatusEnum.class);

        if (taskStatus == TaskStatusEnum.COMPLETED) {
            log.error("精修任务{}已完成{}，但仍请求精修，理论上不存在", id, taskStatus);
            throw new BizException(ResultCode.BIZ_FAIL);
        }
        target.addExtInfo(KEY_REFINE_STATUS, TaskStatusEnum.INIT);

        updateByIdSelective(target);
    }

    @Override
    public void completeRefine(Integer id) {
        CreativeBatchDO data = creativeBatchDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        CreativeBatchVO target = CreativeBatchConverter.do2VO(data);

        String bizTag = target.getExtValue(BIZ_TAG, String.class);
        AssertUtil.assertTrue(StringUtils.equals(bizTag, EXAMPLE_IMAGES), ResultCode.BIZ_FAIL, "当前批次非精选图");

        TaskStatusEnum taskStatus = target.getExtValue(KEY_REFINE_STATUS, TaskStatusEnum.class);
        AssertUtil.assertNotNull(taskStatus, ResultCode.BIZ_FAIL, "精修任务状态为空");

        target.addExtInfo(KEY_REFINE_STATUS, TaskStatusEnum.COMPLETED);

        updateByIdSelective(target);
    }

    @Override
    public void changeTempVideo(Integer id, Integer index, String videoUrl) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");

        AssertUtil.assertTrue(!data.getStatus().isEnd(), ResultCode.BIZ_FAIL, "创作批次已完结");

        if (StringUtils.isNotBlank(videoUrl)) {
            data.addExtInfo(KEY_TEMP_VIDEO + index, videoUrl);
        } else {
            data.getExtInfo().remove(KEY_TEMP_VIDEO + index);
            data.getExtInfo().remove(KEY_TEMP_VIDEO_TASK + index);

            // 同时清空result images，防止影响展示（否则重置创作状态时会有展示问题）
            data.setResultImages(new ArrayList<>());
        }

        updateByIdSelective(data);
    }

    /**
     * 申请创建视频片段
     *
     * @param req 请求
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void apply2GenVideoClip(VideoClipGenReq req) {
        CreativeBatchVO batch = selectById(req.getId());
        AssertUtil.assertNotNull(batch, ResultCode.PARAM_INVALID, "创作批次不存在");

        AssertUtil.assertTrue(!batch.getStatus().isEnd(), ResultCode.BIZ_FAIL, "创作批次已完结");

        AssertUtil.assertNotNull(req.getIndex(), ResultCode.PARAM_INVALID, "视频片段索引不能为空");
        AssertUtil.assertNotBlank(req.getPrompt(), ResultCode.PARAM_INVALID, "视频片段描述不能为空");
        AssertUtil.assertNotNull(req.getDuration(), ResultCode.PARAM_INVALID, "视频片段时长不能为空");

        if (batch.getExtInfo() != null && batch.getExtInfo().containsKey(KEY_TEMP_VIDEO_TASK + req.getIndex())) {
            VideoClipTask task = batch.getExtValue(KEY_TEMP_VIDEO_TASK + req.getIndex(), VideoClipTask.class);
            if (task != null && !StringUtils.equals(task.getTaskStatus(), CommonTaskEnums.TaskStatus.FAILED.name())) {
                log.info("当前视频切片任务已经存在，且其状态为{}，不允许重试", task.getTaskStatus());
                return;
            }
        }

        // 保存切片任务记录
        CommonTaskVO task = new CommonTaskVO();
        {
            task.setUserId(OperationContextHolder.getMasterUserId());
            task.setOperatorId(OperationContextHolder.getOperatorUserId());
            task.setTaskType(CommonTaskEnums.TaskType.VIDEO_GENERATION.name());
            task.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
            task.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.KLING.name());
            task.setRelatedBizType(CommonTaskEnums.RelatedBizType.CREATE_VIDEO_CLIP.getCode());
            task.setRelatedBizId(batch.getId() + "_" + req.getIndex());

            task = commonTaskService.insert(task);
        }

        // 保存切片任务关联到批次
        VideoClipTask batchClipView = new VideoClipTask();
        {
            batchClipView.setId(req.getId());
            batchClipView.setIndex(req.getIndex());
            batchClipView.setPrompt(req.getPrompt());
            batchClipView.setDuration(req.getDuration());
            batchClipView.setTaskId(task.getId());
            batchClipView.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.KLING.name());
            batchClipView.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
        }

        batch.addExtInfo(KEY_TEMP_VIDEO_TASK + req.getIndex(), batchClipView);
        updateByIdSelective(batch);
    }

    @Override
    public void resetProcessing(Integer id) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(data.getType() == CreativeTypeEnum.CREATE_VIDEO, ResultCode.BIZ_FAIL,
            "重置为生成中仅支持视频创作");

        if (!data.getStatus().isEnd()) {
            log.warn("当前状态为" + data.getStatus() + "，无需重置");
            return;
        }
        CreativeBatchVO target = new CreativeBatchVO();
        target.setId(id);
        target.setStatus(CreativeStatusEnum.PROCESSING);
        updateByIdSelective(target);
    }

    @Override
    public String downloadAll(Integer id, List<String> imageUrls) throws IOException {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        List<String> resultImages = data.getResultImages();
        List<String> images = resultImages.stream().filter(imageUrls::contains).collect(Collectors.toList());
        // 保底默认下载全部
        if (CollectionUtils.isEmpty(images)) {
            images = resultImages;
        }
        boolean selectAll = images.size() == resultImages.size();
        boolean updated = false;

        try {
            // 如果已经在扩展信息中存在，则直接返回结果
            String zipUrl = data.getExtValue(KEY_ZIP_URL, String.class);
            String latestZipTime = data.getExtValue(KEY_LATEST_ZIP_TIME, String.class);

            //已经存在zip地址，切最近更新时间一致，则直接返回
            if (selectAll && StringUtils.isNotBlank(zipUrl) && StringUtils.equals(latestZipTime,
                DateUtils.formatTime(data.getModifyTime()))) {
                return zipUrl;
            }

            updated = true;

            String url = ossHelper.createZipFromUrlsAndUpload(images,
                FileUtils.confoundFileName(data.getModelName() + "_" + id), false);

            // 缓存全选的zip文件
            if (selectAll) {
                data.addExtInfo(KEY_ZIP_URL, url);
                data.addExtInfo(KEY_LATEST_ZIP_TIME, DateUtils.formatTime(data.getModifyTime()));
            }

            return url;
        } finally {
            if (OperationContextHolder.getRoleType() == RoleTypeEnum.MERCHANT) {
                // noinspection unchecked
                List<String> downloadImages = data.getExtValue(DOWNLOADED_IMGS, List.class);
                Set<String> downloadImagesSet = downloadImages != null ? new HashSet<>(downloadImages)
                    : new HashSet<>();
                downloadImagesSet.addAll(images);
                data.addExtInfo(DOWNLOADED_IMGS, new ArrayList<>(downloadImagesSet));
                updated = true;
            }

            if (updated) {
                updateByIdSelective(data);
            }
        }
    }

    @Override
    public void assignTo(Integer batchId, Integer userId) {
        CreativeBatchVO data = selectById(batchId);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "创作批次不存在");
        AssertUtil.assertTrue(data.getStatus() == CreativeStatusEnum.FINISHED, ResultCode.BIZ_FAIL, "创作批次未完成");

        data.setOperatorId(userId);
        data.setUserId(userId);
        data.setCreateTime(new Date());
        data.setModifyTime(new Date());
        data.addExtInfo(KEY_ASSIGN_OPERATOR, OperationContextHolder.getOperatorUserId());

        updateByIdSelective(data);
    }

    @Override
    public List<CreativeBatchVO> queryUnCompletedExternal() {
        if (CollectionUtils.isEmpty(CreativeTypeEnum.getExternalTypes())) {
            return Collections.emptyList();
        }
        CreativeBatchExample example = new CreativeBatchExample();
        // 优先处理较早任务
        example.setOrderByClause("modify_time ASC");

        example.createCriteria().andStatusIn(CreativeStatusEnum.getUncompleteStatusList()).andTypeIn(
            CreativeTypeEnum.getExternalTypes()).andDeletedEqualTo(false);

        List<CreativeBatchDO> list = creativeBatchDAO.selectByExample(example);
        return list.stream().map(CreativeBatchConverter::do2VO).collect(Collectors.toList());
    }

    @Override
    public List<String> queryImagesByElement(Integer elementId, Integer userId, Boolean testFlag, Integer limit) {
        // 获取元素及其子元素的ID列表
        List<Integer> elementIds = creativeElementService.getElementIdsWithChildren(elementId);

        // 从批次表查询
        List<CreativeBatchDO> dataList = creativeBatchDAO.selectByElements(elementIds, userId, testFlag, limit);
        List<CreativeBatchVO> list = CreativeBatchConverter.doList2VOList(dataList);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(CreativeBatchVO::getResultImages).filter(Objects::nonNull).flatMap(List::stream)
            .collect(Collectors.toList());
    }

    @Override
    public PageInfo<String> queryImagesByElementWithPage(CreativeBatchQuery query) {
        // 获取元素及其子元素的ID列表
        List<Integer> elementIds = creativeElementService.getElementIdsWithChildren(query.getElementId());
        if (CollectionUtils.isNotEmpty(elementIds)) {
            query.setElementIds(elementIds);
        }

        PageInfo<CreativeBatchVO> data = queryCreativeBatchByPage(query);

        PageInfo<String> result = new PageInfo<>();
        result.setTotalCount(data.getTotalCount());
        result.setSize(data.getSize());
        result.setHasNextPage(data.isHasNextPage());
        result.setExtInfo(data.getExtInfo());

        if (CollectionUtils.isNotEmpty(data.getList())) {
            List<String> collect = data.getList().stream().map(CreativeBatchVO::getResultImages).filter(
                Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
            result.setList(collect);
        }

        return result;
    }

    @Override
    public Integer createAliyunTryonTask(TryonTaskParams params) {

        AssertUtil.assertNotNull(params.getTopUrl(), ResultCode.PARAM_INVALID, "topUrl不能为空");

        String aliyunTaskId = aliyunTryonService.createTryonTask(params.getTopUrl(), params.getBottomUrl(),
            params.getPersonImgUrl());

        AssertUtil.assertNotBlank(aliyunTaskId, ResultCode.BIZ_FAIL, "创建try-on任务失败");

        CommonTaskVO task = new CommonTaskVO();
        {
            task.setUserId(OperationContextHolder.getMasterUserId());
            task.setOperatorId(OperationContextHolder.getOperatorUserId());
            task.setTaskType(CommonTaskEnums.TaskType.TRY_ON.name());
            task.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
            task.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.ALIYUN.name());
            task.setOutTaskId(aliyunTaskId);
            task.setReqBizParams(CommonParamsWrapper.wrap(params));

            task = commonTaskService.insert(task);
        }

        return task.getId();
    }

    @Override
    public Integer createTryonRefinerTask(Integer tryonTaskId, String gender) {
        AssertUtil.assertNotNull(tryonTaskId, ResultCode.PARAM_INVALID, "任务ID不能为空");
        AssertUtil.assertNotBlank(gender, ResultCode.PARAM_INVALID, "性别不能为空");
        AssertUtil.assertTrue("woman".equals(gender) || "man".equals(gender), ResultCode.PARAM_INVALID,
            "性别只能为'woman'或'man'");

        CommonTaskVO tryonTask = commonTaskService.selectById(tryonTaskId);
        AssertUtil.assertNotNull(tryonTask, ResultCode.PARAM_INVALID, "任务不存在");

        AssertUtil.assertTrue(CommonTaskEnums.TaskStatus.isEnd(tryonTask.getTaskStatus()) && StringUtils.isNotBlank(
            tryonTask.getRetDetail()), ResultCode.PARAM_INVALID, "任务未完结，无法精修");

        TryonTaskParams params = CommonParamsWrapper.unwrap(tryonTask.getReqBizParams());
        AssertUtil.assertNotNull(params, ResultCode.PARAM_INVALID, "任务请求参数异常");

        TryonTaskOutputModel outputModel = JSONObject.parseObject(tryonTask.getRetDetail(), TryonTaskOutputModel.class);
        AssertUtil.assertTrue(outputModel != null && StringUtils.isNotBlank(outputModel.getImageUrl()),
            ResultCode.PARAM_INVALID, "任务结果参数异常");

        String tryonResultImgUrl = outputModel.getImageUrl();

        String refinerTaskId = aliyunTryonService.createTryonRefinerTask(params.getTopUrl(), params.getBottomUrl(),
            params.getPersonImgUrl(), tryonResultImgUrl, gender);

        CommonTaskVO refinerTask = new CommonTaskVO();
        {
            refinerTask.setUserId(OperationContextHolder.getMasterUserId());
            refinerTask.setOperatorId(OperationContextHolder.getOperatorUserId());
            refinerTask.setTaskType(CommonTaskEnums.TaskType.TRY_ON_REFINER.name());
            refinerTask.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
            refinerTask.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.ALIYUN.name());
            refinerTask.setOutTaskId(refinerTaskId);

            TryonRefinerTaskParams refinerTaskParams = new TryonRefinerTaskParams();
            {
                refinerTaskParams.setTopUrl(params.getTopUrl());
                refinerTaskParams.setBottomUrl(params.getBottomUrl());
                refinerTaskParams.setPersonImgUrl(params.getPersonImgUrl());
                refinerTaskParams.setTryonTaskResultImgUrl(tryonResultImgUrl);
                refinerTaskParams.setGender(gender);
            }
            refinerTask.setReqBizParams(CommonParamsWrapper.wrap(refinerTaskParams));

            refinerTask = commonTaskService.insert(refinerTask);
        }

        return refinerTask.getId();
    }

    @Override
    public CommonTaskVO queryAliyunTryonTask(Integer taskId) {
        CommonTaskVO task = commonTaskService.selectById(taskId);
        AssertUtil.assertNotNull(task, ResultCode.PARAM_INVALID, "任务不存在");

        syncActiveTryonTaskStatus(task);
        return task;
    }

    private void syncActiveTryonTaskStatus(CommonTaskVO task) {
        if (!CommonTaskEnums.TaskStatus.isEnd(task.getTaskStatus())) {
            TryonTaskOutputModel tryonTaskOutput = aliyunTryonService.queryTask(task.getOutTaskId());
            AssertUtil.assertNotNull(tryonTaskOutput, ResultCode.BIZ_FAIL, "查询try-on任务状态异常");
            CommonTaskEnums.TaskStatus status = CommonTaskEnums.TaskStatus.fromAliyunTaskStatus(
                tryonTaskOutput.getTaskStatus());
            task.setTaskStatus(status.name());
            task.setOutTaskStatus(tryonTaskOutput.getTaskStatus());

            if (task.getTaskStartTime() == null) {
                task.setTaskStartTime(new Date());
            }
            if (CommonTaskEnums.TaskStatus.isEnd(task.getTaskStatus())) {
                task.setTaskEndTime(new Date());
            }
            task.setRetDetail(JSONObject.toJSONString(tryonTaskOutput));

            commonTaskService.updateByIdSelective(task);
        }
    }

    @Override
    public PageInfo<CommonTaskVO> queryAliyunTryonTasksByPage(CommonTaskQuery query) {
        if (StringUtils.isBlank(query.getTaskType()) && CollectionUtils.isEmpty(query.getTaskTypeList())) {
            query.setTaskTypeList(
                Arrays.asList(CommonTaskEnums.TaskType.TRY_ON.name(), CommonTaskEnums.TaskType.TRY_ON_REFINER.name()));
        }

        if (!OperationContextHolder.isAdmin()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }
        PageInfo<CommonTaskVO> page = commonTaskService.queryCommonTaskByPage(query);
        if (CollectionUtils.isNotEmpty(page.getList())) {
            for (CommonTaskVO each : page.getList()) {
                syncActiveTryonTaskStatus(each);
            }
        }

        return page;
    }

    @Override
    public void addDemoTag(Integer id) {
        CreativeBatchVO data = selectById(id);
        AssertUtil.assertNotNull(data, ResultCode.PARAM_INVALID, "任务不存在");

        String demoTag = data.getExtValue(KEY_DEMO_TAG, String.class);
        String target = !StringUtils.equals(YES, demoTag) ? YES : NO;

        data.addExtInfo(KEY_DEMO_TAG, target);
        updateByIdSelective(data);
    }

    @Override
    public int queryCreateImageCntByModelId(Integer modelId) {
        MaterialModelVO modelVO = materialModelService.selectById(modelId);
        AssertUtil.assertNotNull(modelVO, ResultCode.PARAM_INVALID, "模型不存在");

        Long cnt = creativeBatchDAO.queryCreateImageCntByModelId(modelId, modelVO.getUserId(), false);
        return cnt == null ? 0 : cnt.intValue();
    }

    @Override
    public int queryFinishedSysGenImageCntByModelId(Integer modelId) {
        Long cnt = creativeBatchDAO.queryCreateImageCntByModelId(modelId,
            CommonUtil.mockAutoCreativeContext().getMasterUser(), true);
        return cnt == null ? 0 : cnt.intValue();
    }

    @Override
    public List<UserCountVO> statsQueuedUser() {
        CreativeBatchExample example = new CreativeBatchExample();
        Criteria criteria = example.createCriteria();
        criteria.andStatusIn(Arrays.asList(CreativeStatusEnum.INIT.getCode(), CreativeStatusEnum.QUEUE.getCode()));
        criteria.andDeletedEqualTo(false).andTypeNotIn(CreativeTypeEnum.getManualTypes());
        if (CollectionUtils.isNotEmpty(CreativeTypeEnum.getExternalTypes())) {
            criteria.andTypeNotIn(CreativeTypeEnum.getExternalTypes());
        }
        criteria.andUserTypeIsCustomer();

        List<UserCountDO> doList = creativeBatchDAO.queryQueuedUser(example);
        return CreativeBatchConverter.userCount2VO(doList);
    }

    @Override
    public StatsQueuedCreativeDO statsQueuedCreative() {
        return creativeBatchDAO.statsQueuedCreative();
    }

    @Override
    public List<StatsUserQueuedCreativeDO> statsCustomerQueuedCreative() {
        return creativeBatchDAO.statsCustomerQueue();
    }

    @Override
    public List<StatsUserQueuedCreativeDO> statsBackUserQueuedCreative() {
        return creativeBatchDAO.statsBackUserQueue();
    }

    /**
     * 基于用户维度查询未完成的批次
     *
     * @param pipelineId        集群id
     * @param limit             查询条数
     * @param exceptTypeList    排除列表
     * @param includeTypeList   包含列表
     * @param includeProcessing 是否包含处理中的
     * @return 批次列表
     */
    private List<CreativeBatchVO> queryUnCompletedTopUser(Integer pipelineId, Integer limit,
                                                          List<String> exceptTypeList, List<String> includeTypeList,
                                                          boolean includeProcessing) {
        Map<String, Object> params = new HashMap<>();
        params.put("pipelineId", pipelineId);
        params.put("limit", limit);

        // 忙时每个客户最大数、
        int maxPerUser = systemConfigService.calcMaxPerCustomer();
        params.put("maxPerUser", maxPerUser);
        params.put("maxAutoCreateSize", 10);

        List<String> exceptTypes = new ArrayList<>();
        exceptTypes.addAll(CreativeTypeEnum.getManualTypes());
        exceptTypes.addAll(CreativeTypeEnum.getExternalTypes());
        if (CollectionUtils.isNotEmpty(exceptTypeList)) {
            exceptTypes.addAll(exceptTypeList);
        }
        if (CollectionUtils.isNotEmpty(exceptTypes)) {
            params.put("exceptTypes", exceptTypes);
        }

        // 添加包含类型
        if (CollectionUtils.isNotEmpty(includeTypeList)) {
            params.put("includeTypes", includeTypeList);
        }

        if (includeProcessing) {
            params.put("includeProcessing", true);
        }

        JSONArray vipUserList = systemConfigService.queryJsonArrValue(CREATIVE_VIP_USERS);
        if (CollectionUtils.isNotEmpty(vipUserList)) {
            params.put("vipUserList", vipUserList.toJavaList(Integer.class));
        }

        List<CreativeBatchDO> list = creativeBatchDAO.selectUnCompletedTopUser(params);
        log.info("queryUnCompletedTopUser: ids={}", list.stream().map(CreativeBatchDO::getId));
        return CreativeBatchConverter.doList2VOList(list);
    }

    /**
     * 查询示例图创作批次
     *
     * @param modelId 模型id
     * @return 创作批次
     */
    private CreativeBatchVO queryExampleImagesItem(Integer modelId) {
        CreativeBatchQuery query = new CreativeBatchQuery();
        query.setModelId(modelId);

        if (!OperationContextHolder.isBackRole() && !OperationContextHolder.isDistributorRole()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }

        query.setBizTag(EXAMPLE_IMAGES);
        query.setPageSize(1);
        query.setPageNum(1);

        List<CreativeBatchVO> list = queryCreativeBatchList(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }

    private String getQueryLastedTimeKey(List<String> types, ModelTypeEnum modelType) {
        // 按types的字母顺序排序
        types.sort(Comparator.naturalOrder());

        // 遍历types，拼接成以竖线分隔的字符串
        StringBuilder sb = new StringBuilder();
        for (String type : types) {
            sb.append(type).append("|");
        }

        return KEY_CREATIVE_QUERY_LASTED_TIME + "_" + modelType + "_" + sb + "_"
               + OperationContextHolder.getOperatorUserId();
    }

    /**
     * 填充昵称
     *
     * @param data 批次列表
     */
    private void fillNickName(List<CreativeBatchVO> data) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        List<Integer> userIds = data.stream().map(CreativeBatchVO::getUserId).collect(Collectors.toList());
        userIds.addAll(data.stream().map(CreativeBatchVO::getOperatorId).collect(Collectors.toList()));
        List<UserVO> userList = userService.batchQueryById(new ArrayList<>(new HashSet<>(userIds)));

        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        data.forEach(vo -> {
            vo.setUserNick(userList.stream().filter(u -> u.getId().equals(vo.getUserId())).findFirst()
                .map(UserVO::getNickName).orElse(null));
            vo.setOperatorNick(userList.stream().filter(u -> u.getId().equals(vo.getOperatorId())).findFirst()
                .map(UserVO::getNickName).orElse(null));
        });
    }

    /**
     * 填充元素信息
     *
     * @param result       批次列表
     * @param isSelectTask 是否查询批次对应任务信息
     */
    private void fillElementInfo(List<CreativeBatchVO> result, boolean isSelectTask) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        // 获取批次id列表
        List<Integer> batchIds = result.stream().map(CreativeBatchVO::getId).collect(Collectors.toList());

        // 批量查询批次元素
        Map<Integer, List<CreativeElementVO>> elementMap = creativeBatchElementsService.batchQueryBatchElements(
            batchIds);

        // 批量查询对应任务列表数据
        final Map<Integer, List<CreativeTaskVO>> taskMap = isSelectTask ? creativeTaskService.batchQueryCreativeTask(
            batchIds) : new HashMap<>();

        // 填充元素信息
        result.forEach(item -> {
            // 如果为 true 则查询批次对应任务列表
            if (isSelectTask && taskMap != null) {
                List<CreativeTaskVO> tasksLIst = taskMap.get(item.getId());
                if (CollectionUtils.isNotEmpty(tasksLIst)) {
                    item.setCreativeTasksList(tasksLIst);
                }
            }

            // 填充元素信息
            List<CreativeElementVO> elements = elementMap.get(item.getId());
            if (CollectionUtils.isNotEmpty(elements)) {
                elements.forEach(element -> {
                    if (StringUtils.equals(element.getConfigKey(), ElementConfigKeyEnum.FACE.name())) {
                        item.setFaceName(element.getName());
                    }
                    if (StringUtils.equals(element.getConfigKey(), ElementConfigKeyEnum.SCENE.name())) {
                        item.setSceneName(element.getName());
                    }
                });
            }
        });
    }

    /**
     * 用于前端的'历史任务'详情页面：创作的脸和场景名
     *
     * @param ret          结果模型
     * @param isSelectTask 是否查询任务列表
     */
    private void fillElements(CreativeBatchVO ret, Boolean... isSelectTask) {
        fillElements(Collections.singletonList(ret), isSelectTask);
    }

    /**
     * 用于前端的'历史任务'详情页面：创作的脸和场景名
     *
     * @param batchList    结果模型
     * @param isSelectTask 是否查询任务列表
     */
    private void fillElements(List<CreativeBatchVO> batchList, Boolean... isSelectTask) {
        if (CollectionUtils.isEmpty(batchList)) {
            return;
        }
        Map<Integer, List<CreativeElementVO>> elementMap = creativeBatchElementsService.batchQueryBatchElements(
            batchList.stream().map(CreativeBatchVO::getId).collect(Collectors.toList()));

        if (MapUtils.isNotEmpty(elementMap)) {
            for (CreativeBatchVO batch : batchList) {
                List<CreativeElementVO> batchElements = elementMap.get(batch.getId());
                if (CollectionUtils.isEmpty(batchElements)) {
                    continue;
                }

                batchElements.forEach(e -> {
                    if (StringUtils.equalsIgnoreCase(e.getConfigKey(), ElementConfigKeyEnum.FACE.name())) {
                        batch.setFaceName(e.getName());
                    } else if (StringUtils.equalsIgnoreCase(e.getConfigKey(), ElementConfigKeyEnum.SCENE.name())) {
                        batch.setSceneName(e.getName());
                    }
                });

                if (StringUtils.isBlank(batch.getFaceName())) {
                    batch.setFaceName(batch.getStringFromExtInfo(CommonConstants.KEY_SNAPSHOT_FACE_NAME));
                }
                if (StringUtils.isBlank(batch.getSceneName())) {
                    batch.setSceneName(batch.getStringFromExtInfo(CommonConstants.KEY_SNAPSHOT_SCENE_NAME));
                }
            }
        }

        // 填充任务列表
        batchList.forEach(batch -> {
            if (isSelectTask != null && isSelectTask.length > 0 && isSelectTask[0]) {
                List<CreativeTaskVO> creativeTaskVOS = creativeTaskService.queryTaskByBatchId(batch.getId());
                batch.setCreativeTasksList(creativeTaskVOS);
            }

            // 逻辑删除时的补偿：快照里没有，查数据库，如果是逻辑删除状态，则返回，同时以快照填充到ext info
            if (StringUtils.isBlank(batch.getFaceName()) || StringUtils.isBlank(batch.getSceneName())) {
                CreativeBatchElementsQuery query = new CreativeBatchElementsQuery();
                query.setBatchId(batch.getId());
                List<CreativeBatchElementsVO> batchElements
                    = creativeBatchElementsService.queryCreativeBatchElementsList(query);

                for (CreativeBatchElementsVO e : batchElements) {
                    fetchElementFromDB(batch, e);
                }
            }
        });
    }

    /**
     * 设置交付信息
     *
     * @param vos 批次列表
     */
    private void fillDeliveryInfo(List<CreativeBatchVO> vos) {
        //只设置后台用户的信息
        if (!OperationContextHolder.isBackRole()) {
            return;
        }

        List<Integer> ids = vos.stream().filter(item -> item.getExtInfo(KEY_DELIVERY_OPERATOR, Integer.class) != null)
            .map(item -> item.getExtInfo(KEY_DELIVERY_OPERATOR, Integer.class)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        List<UserVO> users = userService.batchQueryById(ids);
        vos.forEach(item -> {
            if (item.getExtInfo(KEY_DELIVERY_OPERATOR, Integer.class) == null) {
                return;
            }
            UserVO user = users.stream().filter(
                u -> u.getId().equals(item.getExtInfo(KEY_DELIVERY_OPERATOR, Integer.class))).findFirst().orElse(null);
            item.setDeliveryName(user != null ? user.getNickName() : null);
        });
    }

    /**
     * 填充模型信息
     *
     * @param vo 批次
     */
    private void fillModelInfo(CreativeBatchVO vo) {
        fillModelInfo(Collections.singletonList(vo));
    }

    /**
     * 填充模型信息
     *
     * @param vos 批次列表
     */
    private void fillModelInfo(List<CreativeBatchVO> vos) {
        List<Integer> ids = vos.stream().map(CreativeBatchVO::getModelId).filter(Objects::nonNull).collect(
            Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        List<MaterialModelVO> list = materialModelService.batchQueryByIds(new ArrayList<>(new HashSet<>(ids)));
        vos.forEach(item -> {
            if (item.getModelId() == null) {
                return;
            }
            MaterialModelVO model = list.stream().filter(m -> m.getId().equals(item.getModelId())).findFirst().orElse(
                null);

            if (model == null) {
                return;
            }

            item.setModelName(model.getName());
            item.setModelShowImg(model.getShowImage());

            CreativeBatchConverter.fillModelExt(item, model);
        });
    }

    private void fetchElementFromDB(CreativeBatchVO ret, CreativeBatchElementsVO e) {

        // 这里用selectById，因为可能存在删除的元素deleted=1（脸、场景）
        CreativeElementVO eleLogicDeleted = creativeElementService.selectById(e.getElementId());
        if (eleLogicDeleted != null) {

            if (StringUtils.equalsIgnoreCase(eleLogicDeleted.getConfigKey(), ElementConfigKeyEnum.FACE.name())) {
                ret.setFaceName(eleLogicDeleted.getName());

                // 快照字段：脸名
                if (ret.getExtInfo() != null && !ret.getExtInfo().containsKey(CommonConstants.KEY_SNAPSHOT_FACE_NAME)) {
                    saveNameSnapshot(ret, CommonConstants.KEY_SNAPSHOT_FACE_NAME, eleLogicDeleted, e);
                }
            }

            if (StringUtils.equalsIgnoreCase(eleLogicDeleted.getConfigKey(), ElementConfigKeyEnum.SCENE.name())) {
                ret.setSceneName(eleLogicDeleted.getName());

                // 快照字段：场景名
                if (ret.getExtInfo() != null && !ret.getExtInfo().containsKey(
                    CommonConstants.KEY_SNAPSHOT_SCENE_NAME)) {
                    saveNameSnapshot(ret, CommonConstants.KEY_SNAPSHOT_SCENE_NAME, eleLogicDeleted, e);
                }
            }
        }
    }

    private void saveNameSnapshot(CreativeBatchVO ret, String keySnapshotFaceName, CreativeElementVO eleLogicDeleted,
                                  CreativeBatchElementsVO e) {
        CreativeBatchVO target = new CreativeBatchVO();
        target.setId(ret.getId());
        JSONObject extInfo = new JSONObject();
        if (ret.getExtInfo() != null) {
            extInfo.putAll(ret.getExtInfo());
        }
        extInfo.put(keySnapshotFaceName, eleLogicDeleted.getName());
        target.setExtInfo(extInfo);

        log.warn("当前创作记录id={}使用的元素id={},name={}被逻辑删除，保存到extInfo作为快照", ret.getId(),
            e.getElementId(), eleLogicDeleted.getName());
        this.updateByIdSelective(target);
    }

    private void fillVideoClipTaskStatus(CreativeBatchVO data) {
        List<VideoClipTask> videoClipTasks = data.getVideoClipGenTasks();
        if (CollectionUtils.isNotEmpty(videoClipTasks)) {
            CommonTaskQuery query = new CommonTaskQuery();
            query.setIdList(videoClipTasks.stream().map(VideoClipTask::getTaskId).collect(Collectors.toList()));
            List<CommonTaskVO> tasks = commonTaskService.queryCommonTaskList(query);
            Map<Integer, CommonTaskVO> map = tasks.stream().collect(
                Collectors.toMap(CommonTaskVO::getId, Function.identity()));
            for (VideoClipTask batchClip : videoClipTasks) {
                CommonTaskVO task = map.get(batchClip.getTaskId());
                if (task != null) {
                    batchClip.setStartTime(task.getTaskStartTime());
                    batchClip.setEndTime(task.getTaskEndTime());
                }
            }

            data.setVideoClipGenTasks(videoClipTasks);
        }
    }

    @Override
    public List<CreativeBatchVO> getLatestCreativeBatchByPoseIds(List<Integer> poseIdList) {
        if (CollectionUtils.isEmpty(poseIdList)) {
            return new ArrayList<>();
        }

        // 调用DAO层方法，使用窗口函数在数据库层面进行优化查询
        List<CreativeBatchDO> doList = creativeBatchDAO.selectLatestByPoseIds(poseIdList);

        if (CollectionUtils.isEmpty(doList)) {
            return new ArrayList<>();
        }

        // 填充元素信息（可选）
        // if (CollectionUtils.isNotEmpty(result)) {
        //     fillElements(result);
        // }

        // 转换为VO对象
        return CreativeBatchConverter.doList2VOList(doList);
    }

    /**
     * 从CreativeTaskVO中提取faceId
     */
    private Integer extractFaceIdFromTask(CreativeTaskVO task) {
        if (task == null || task.getTplInfo() == null) {
            return null;
        }
        Object faceObj = task.getTplInfo().getTplParams().get("FACE");
        if (faceObj instanceof Map) {
            Object idObj = ((Map<?, ?>)faceObj).get("id");
            return parseToInteger(idObj);
        }
        return null;
    }

    /**
     * 从CreativeTaskVO中提取sceneId
     */
    private Integer extractSceneIdFromTask(CreativeTaskVO task) {
        if (task == null || task.getTplInfo() == null) {
            return null;
        }
        Object sceneObj = task.getTplInfo().getTplParams().get("SCENE");
        if (sceneObj instanceof Map) {
            Object idObj = ((Map<?, ?>)sceneObj).get("id");
            return parseToInteger(idObj);
        }
        return null;
    }

    /**
     * 判断当前任务是否需要被统计进入 batch 中
     *
     * @param target      batch 信息
     * @param finishedCnt 已完成的任务数量
     * @param task        任务信息
     */
    private void reCheckTaskIsNeedStats(CreativeBatchVO target, AtomicInteger finishedCnt, CreativeTaskVO task) {
        // 提取关键标识
        Boolean isNeedStorageResult = task.getBooleanFromExtInfo(KEY_IS_NEED_STORAGE_RESULT);

        // 当等于 null 或者 isNeedStorageResult为 true 时，则需要被统计
        if (isNeedStorageResult == null || isNeedStorageResult) {
            target.addResultImage(task.getResultImages());
            finishedCnt.set(finishedCnt.get() + 1);
        }
    }

}