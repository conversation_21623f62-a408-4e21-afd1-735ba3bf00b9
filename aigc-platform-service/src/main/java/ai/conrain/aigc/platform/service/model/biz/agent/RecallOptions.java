package ai.conrain.aigc.platform.service.model.biz.agent;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 召回配置选项
 */
@Data
public class RecallOptions implements Serializable {

    // 是否启用流派并发查询模式
    private Boolean enableGenreConcurrency = true;

    // 款式相似度阈值
    private Double styleSimilarityThreshold = 0.5;

    // 款式匹配的图片数量
    private Integer styleRecallCount = 2000;

    // 服装款式类型白名单规则
    // key: 参考图的二级分类（如"大衣"），value: 允许召回的款式二级分类列表（如["大衣", "风衣", "派克大衣", "夹克"]）
    private Map<String, List<String>> styleWhitelistRules;

    // 服装款式类型黑名单规则
    // key: 参考图的二级分类（如"大衣"），value: 禁止召回的款式二级分类列表
    private Map<String, List<String>> styleBlacklistRules;

    // 是否启用款式类型过滤
    private Boolean enableStyleTypeFilter = false;

    // 服装功能分类白名单规则
    // key: 参考图的功能分类（如"运动服"），value: 允许召回的功能分类列表（如["运动服", "户外"]）
    private Map<String, List<String>> functionWhitelistRules;

    // 服装功能分类黑名单规则
    // key: 参考图的功能分类（如"运动服"），value: 禁止召回的功能分类列表
    private Map<String, List<String>> functionBlacklistRules;

    // 是否启用功能分类过滤
    private Boolean enableFunctionFilter = false;

    // 服装构图黑名单规则（包含原构图黑名单和镜头构图黑名单）
    // key: 参考图的服装类型（如"OUTERWEAR", "TOPS", "SKIRTS", "PANTS", "DRESS", "SUITS"），
    // value: 禁止召回的构图类型列表（如["LOWER_BODY_CLOSE_UP", "UPPER_BODY_CLOSE_UP", "LONG_SHOT", "FULL_BODY", "BUST_SHOT"]）
    private Map<String, List<String>> compositionBlacklistRules;

    // 是否启用构图过滤
    private Boolean enableCompositionFilter = false;
}