package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;
import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * GalleryQuery
 *
 * @version GalleryService.java v 0.1 2025-08-23 02:05:18
 */
@Data
public class GalleryQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

        /** id */
        private Integer id;

        /** 用户id */
        private Integer userId;

        /** 操作人id */
        private Integer operatorId;

        /** ClothFirstCategory(按功能分) */
        private String type;

        /** 二级分类(功能内细分) */
        private String subType;

        /** 按标签筛选 OR */
        private List<String> tagsIncludes;

        /** 按标签组筛选 (组内 OR, 组间 AND) */
        private List<List<String>> tagsIncludesGroups;

        /** 单张图片url */
        private String imageUrl;

        /** md5值 */
        private String md5;

        /** 归属 */
        private String belong;

        /** 创建时间 */
        private Date createTime;

        /** 修改时间 */
        private Date modifyTime;


        /** 配置信息 */
        private String extInfo;

        /** 翻页参数：页面大小 */
        private Integer pageSize;

        /** 翻页参数：页数（从1开始） */
        private Integer pageNum;

        /** 排序指令（示例："id asc"） */
        private String orderBy;

}