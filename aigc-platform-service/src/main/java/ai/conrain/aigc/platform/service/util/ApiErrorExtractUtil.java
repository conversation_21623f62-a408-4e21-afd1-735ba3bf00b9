package ai.conrain.aigc.platform.service.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * API错误信息提取工具类
 * 用于从复杂的API错误响应中提取关键信息，提供简洁明了的错误摘要
 */
public class ApiErrorExtractUtil {

    /**
     * 从API错误响应中提取关键信息
     * @param errorResponse 完整的错误响应字符串
     * @return 包含关键信息的Map对象
     */
    public static Map<String, String> extractErrorInfo(String errorResponse) {
        Map<String, String> errorInfo = new HashMap<>();

        if (errorResponse == null || errorResponse.trim().isEmpty()) {
            errorInfo.put("Error", "Empty error response");
            return errorInfo;
        }

        try {
            // 1. 提取HTTP状态码
            extractHttpStatus(errorResponse, errorInfo);
            
            // 2. 识别并提取主要错误类型
            extractMainErrorType(errorResponse, errorInfo);
            
            // 3. 提取关键错误信息（根据错误类型）
            extractSpecificErrorInfo(errorResponse, errorInfo);
            
            // 4. 提取错误发生的主要位置
            extractErrorLocation(errorResponse, errorInfo);
            
        } catch (Exception e) {
            errorInfo.put("Parse Error", "Failed to parse: " + e.getMessage());
        }
        
        return errorInfo;
    }

    /**
     * 提取HTTP状态码
     */
    private static void extractHttpStatus(String errorResponse, Map<String, String> errorInfo) {
        Pattern pattern = Pattern.compile("(\\d{3})\\s+[A-Za-z\\s]+Error");
        Matcher matcher = pattern.matcher(errorResponse);
        if (matcher.find()) {
            errorInfo.put("HTTP Status", matcher.group(1));
        }
    }

    /**
     * 提取主要错误类型
     */
    private static void extractMainErrorType(String errorResponse, Map<String, String> errorInfo) {
        // CUDA内存错误
        if (errorResponse.contains("CUDA out of memory")) {
            errorInfo.put("Error Type", "CUDA Memory Error");
            extractCudaMemoryInfo(errorResponse, errorInfo);
            return;
        }
        
        // TorchScript错误
        if (errorResponse.contains("TorchScript interpreter")) {
            errorInfo.put("Error Type", "TorchScript Error");
            return;
        }
        
        // 一般运行时错误
        Pattern runtimeErrorPattern = Pattern.compile("RuntimeError: ([^\\n\\r]{1,100})");
        Matcher matcher = runtimeErrorPattern.matcher(errorResponse);
        if (matcher.find()) {
            String errorMsg = matcher.group(1).trim();
            // 截断过长的错误信息
            if (errorMsg.length() > 80) {
                errorMsg = errorMsg.substring(0, 80) + "...";
            }
            errorInfo.put("Error Type", "Runtime Error");
            errorInfo.put("Error Message", errorMsg);
            return;
        }
        
        // 系统错误
        if (errorResponse.contains("System error")) {
            errorInfo.put("Error Type", "System Error");
        }
    }

    /**
     * 提取CUDA内存相关信息
     */
    private static void extractCudaMemoryInfo(String errorResponse, Map<String, String> errorInfo) {
        // 提取尝试分配的内存大小
        Pattern memoryPattern = Pattern.compile("Tried to allocate ([0-9.]+ [A-Za-z]+)");
        Matcher matcher = memoryPattern.matcher(errorResponse);
        if (matcher.find()) {
            String memoryRequested = matcher.group(1);
            errorInfo.put("Error Detail", "Tried to allocate " + memoryRequested);
        } else {
            // 如果没有找到具体内存信息，使用通用描述
            errorInfo.put("Error Details", "显存不足");
        }
    }

    /**
     * 提取具体错误信息（根据错误类型）
     */
    private static void extractSpecificErrorInfo(String errorResponse, Map<String, String> errorInfo) {
        String errorType = errorInfo.get("Error Type");
        
        if ("CUDA Memory Error".equals(errorType)) {
            // CUDA错误已在extractCudaMemoryInfo中处理
            return;
        }
        
        if ("TorchScript Error".equals(errorType)) {
            // 提取TorchScript中最关键的错误位置
            Pattern torchErrorPattern = Pattern.compile("File \"([^\"]*/__torch__[^\"]*)\", line (\\d+)");
            Matcher matcher = torchErrorPattern.matcher(errorResponse);
            if (matcher.find()) {
                String fileName = matcher.group(1);
                // 只保留文件名部分
                String shortFileName = fileName.substring(fileName.lastIndexOf('/') + 1);
                errorInfo.put("Error Location", shortFileName + ":" + matcher.group(2));
            }
        }
    }

    /**
     * 提取错误发生的主要位置
     */
    private static void extractErrorLocation(String errorResponse, Map<String, String> errorInfo) {
        if (errorInfo.containsKey("Error Location")) {
            return; // 已经提取过了
        }
        
        // 提取第一个有意义的Python文件位置
        Pattern filePattern = Pattern.compile("File \"([^\"]+\\.py)\", line (\\d+)");
        Matcher matcher = filePattern.matcher(errorResponse);
        if (matcher.find()) {
            String filePath = matcher.group(1);
            String fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
            errorInfo.put("Source Location", fileName + ":" + matcher.group(2));
        }
    }
}
