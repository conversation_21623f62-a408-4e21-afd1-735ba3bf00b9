package ai.conrain.aigc.platform.service.config;

import ai.conrain.aigc.platform.service.enums.LensCompositionType;
import ai.conrain.aigc.platform.service.model.ClothingRuleResult;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PreCaptionRules {

    // 一级分类枚举
    public enum ClothFirstCategory {
        外套("Outerwear", "外套"),
        冬季外套("Winter_Outerwear", "冬季外套"),
        上衣("Tops", "上衣"),
        半裙("Skirts", "半裙"),
        裤子("Pants", "裤子"),
        连衣裙("Dress", "连衣裙"),
        连体衣("Jumpsuit", "连体衣"),
        套装("Suits", "套装");

        private final String code;
        private final String desc;

        ClothFirstCategory(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() { return code; }
        public String getDesc() { return desc; }
    }

    // 外套类枚举
    public enum 外套类型 {
        西装外套("Suit_Jacket", "西装外套", PreCaptionRules.ClothFirstCategory.外套),
        新中式外套("New_Chinese_Style", "新中式外套", PreCaptionRules.ClothFirstCategory.外套),
        开衫("Cardigan", "开衫", PreCaptionRules.ClothFirstCategory.外套),
        夹克("Jacket", "夹克", PreCaptionRules.ClothFirstCategory.外套),
        风衣("Windbreaker", "风衣", PreCaptionRules.ClothFirstCategory.外套),
        大衣("Coat", "大衣", PreCaptionRules.ClothFirstCategory.外套),
        其它外套("Others", "其它", PreCaptionRules.ClothFirstCategory.外套);

        private final String code;
        private final String desc;
        private final PreCaptionRules.ClothFirstCategory category;

        外套类型(String code, String desc, PreCaptionRules.ClothFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public PreCaptionRules.ClothFirstCategory getCategory() {
            return category;
        }
    }

    // 冬季外套类枚举
    public enum 冬季外套类型 {
        羽绒服("Down_Jacket", "羽绒服", PreCaptionRules.ClothFirstCategory.冬季外套),
        皮草("Fur", "皮草", PreCaptionRules.ClothFirstCategory.冬季外套),
        派克大衣("Parka", "派克大衣", PreCaptionRules.ClothFirstCategory.冬季外套),
        棉服("Cotton_Coat", "棉服", PreCaptionRules.ClothFirstCategory.冬季外套);

        private final String code;
        private final String desc;
        private final PreCaptionRules.ClothFirstCategory category;

        冬季外套类型(String code, String desc, PreCaptionRules.ClothFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public PreCaptionRules.ClothFirstCategory getCategory() {
            return category;
        }
    }

    // 上衣类枚举
    public enum 上衣类型 {
        新中式上衣("New_Chinese_Style_Top", "新中式上衣", PreCaptionRules.ClothFirstCategory.上衣),
        卫衣("Hoodie", "卫衣", PreCaptionRules.ClothFirstCategory.上衣),
        毛衣("Sweater", "毛衣", PreCaptionRules.ClothFirstCategory.上衣),
        短袖T恤("Short_Sleeve_T-shirt", "短袖T恤", PreCaptionRules.ClothFirstCategory.上衣),
        吊带("Camisole", "吊带", PreCaptionRules.ClothFirstCategory.上衣),
        打底衫("Base_Layer", "打底衫", PreCaptionRules.ClothFirstCategory.上衣),
        衬衫("Shirt", "衬衫", PreCaptionRules.ClothFirstCategory.上衣),
        T恤("T-shirt", "T恤", PreCaptionRules.ClothFirstCategory.上衣),
        POLO衫("Polo_Shirt", "POLO衫", PreCaptionRules.ClothFirstCategory.上衣),
        其它上衣("Others", "其它", PreCaptionRules.ClothFirstCategory.上衣);

        private final String code;
        private final String desc;
        private final PreCaptionRules.ClothFirstCategory category;

        上衣类型(String code, String desc, PreCaptionRules.ClothFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public PreCaptionRules.ClothFirstCategory getCategory() {
            return category;
        }
    }

    // 半裙类枚举
    public enum 半裙类型 {
        新中式半裙("New_Chinese_Style_Skirt", "新中式半裙", PreCaptionRules.ClothFirstCategory.半裙),
        牛仔裙("Denim_Skirt", "牛仔裙", PreCaptionRules.ClothFirstCategory.半裙),
        百褶裙("Pleated_Skirt", "百褶裙", PreCaptionRules.ClothFirstCategory.半裙),
        运动裙("Sports_Skirt", "运动裙", PreCaptionRules.ClothFirstCategory.半裙),
        短裙("Mini_Skirt", "短裙", PreCaptionRules.ClothFirstCategory.半裙),
        打底裙("Base_Skirt", "打底裙", PreCaptionRules.ClothFirstCategory.半裙),
        A字裙("A_Line_Skirt", "A字裙", PreCaptionRules.ClothFirstCategory.半裙),
        其它半裙("Others", "其它", PreCaptionRules.ClothFirstCategory.半裙);

        private final String code;
        private final String desc;
        private final PreCaptionRules.ClothFirstCategory category;

        半裙类型(String code, String desc, PreCaptionRules.ClothFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public PreCaptionRules.ClothFirstCategory getCategory() {
            return category;
        }
    }

    // 裤子类枚举
    public enum 裤子类型 {
        运动裤("Sports_Pants", "运动裤", PreCaptionRules.ClothFirstCategory.裤子),
        西装裤("Dress_Pants", "西装裤", PreCaptionRules.ClothFirstCategory.裤子),
        牛仔裤("Jeans", "牛仔裤", PreCaptionRules.ClothFirstCategory.裤子),
        工装裤("Cargo_Pants", "工装裤", PreCaptionRules.ClothFirstCategory.裤子),
        短裤("Shorts", "短裤", PreCaptionRules.ClothFirstCategory.裤子),
        登山裤("Hiking_Pants", "登山裤", PreCaptionRules.ClothFirstCategory.裤子),
        打底裤("Leggings", "打底裤", PreCaptionRules.ClothFirstCategory.裤子),
        其它裤子("Others", "其它", PreCaptionRules.ClothFirstCategory.裤子);

        private final String code;
        private final String desc;
        private final PreCaptionRules.ClothFirstCategory category;

        裤子类型(String code, String desc, PreCaptionRules.ClothFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public PreCaptionRules.ClothFirstCategory getCategory() {
            return category;
        }
    }

    // 连衣裙类枚举
    public enum 连衣裙类型 {
        新中式连衣裙("New_Chinese_Style_Dress", "新中式连衣裙", PreCaptionRules.ClothFirstCategory.连衣裙),
        公主裙("Princess_Dress", "公主裙", PreCaptionRules.ClothFirstCategory.连衣裙),
        其它连衣裙("Others", "其它", PreCaptionRules.ClothFirstCategory.连衣裙);

        private final String code;
        private final String desc;
        private final PreCaptionRules.ClothFirstCategory category;

        连衣裙类型(String code, String desc, PreCaptionRules.ClothFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public PreCaptionRules.ClothFirstCategory getCategory() {
            return category;
        }
    }

    // 套装类枚举
    public enum 套装类型 {
        职业套装("Business_suit", "职业套装", PreCaptionRules.ClothFirstCategory.套装),
        西装套装("Business_Attire", "西装套装", PreCaptionRules.ClothFirstCategory.套装),
        休闲套装("Matching_Set", "休闲套装", PreCaptionRules.ClothFirstCategory.套装),
        新中式套装("New_Chinese_Style_Suit", "新中式套装", PreCaptionRules.ClothFirstCategory.套装),
        汉服("Hanfu", "汉服", PreCaptionRules.ClothFirstCategory.套装),
        其它套装("Others", "其它", PreCaptionRules.ClothFirstCategory.套装);

        private final String code;
        private final String desc;
        private final PreCaptionRules.ClothFirstCategory category;

        套装类型(String code, String desc, PreCaptionRules.ClothFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public PreCaptionRules.ClothFirstCategory getCategory() {
            return category;
        }
    }

    // 服装用途枚举


    // 定义映射关系：ClothFirstCategory -> 二级分类 -> 白名单 & 黑名单
    private static final Map<ClothFirstCategory, Map<Enum<?>, List<Enum<?>>>> CLOTH_REF_WHITE_LIST = new HashMap<>();
    private static final Map<ClothFirstCategory, Map<Enum<?>, List<Enum<?>>>> CLOTH_COMPOSITION_BLACK_LIST = new HashMap<>();


    static {
        // 初始化数据
        initClothStyleWhiteList();
        initClothCompositionBlackList();

    }

    /**
     * 根据一级分类和二级分类获取服装规则结果
     *
     * @param firstLevel  ClothFirstCategory（英文）
     * @param secondLevel 二级分类（英文）
     * @return 服装规则结果对象，包含样式白名单、构图黑名单和用途白名单
     */
    public static ClothingRuleResult getStyleRules(ClothFirstCategory firstLevel, Enum<?> secondLevel) {
        List<String> styleWhiteList = new ArrayList<>();
        List<String> compositionBlackList = new ArrayList<>();
        List<String> usageWhiteList = new ArrayList<>();

        // 查询样式白名单
        if (CLOTH_REF_WHITE_LIST.containsKey(firstLevel)) {
            Map<Enum<?>, List<Enum<?>>> secondLevelMap = CLOTH_REF_WHITE_LIST.get(firstLevel);
            if (secondLevelMap.containsKey(secondLevel)) {
                for (Enum<?> item : secondLevelMap.get(secondLevel)) {
                    if (item instanceof 外套类型) {
                        styleWhiteList.add(((外套类型) item).getCode());
                    } else if (item instanceof 冬季外套类型) {
                        styleWhiteList.add(((冬季外套类型) item).getCode());
                    } else if (item instanceof 上衣类型) {
                        styleWhiteList.add(((上衣类型) item).getCode());
                    } else if (item instanceof 半裙类型) {
                        styleWhiteList.add(((半裙类型) item).getCode());
                    } else if (item instanceof 裤子类型) {
                        styleWhiteList.add(((裤子类型) item).getCode());
                    } else if (item instanceof 连衣裙类型) {
                        styleWhiteList.add(((连衣裙类型) item).getCode());
                    } else if (item instanceof 套装类型) {
                        styleWhiteList.add(((套装类型) item).getCode());
                    }
                }
            }
        }

        // 查询构图黑名单
        if (CLOTH_COMPOSITION_BLACK_LIST.containsKey(firstLevel)) {
            Map<Enum<?>, List<Enum<?>>> secondLevelMap = CLOTH_COMPOSITION_BLACK_LIST.get(firstLevel);
            if (secondLevelMap.containsKey(secondLevel)) {
                for (Enum<?> item : secondLevelMap.get(secondLevel)) {
                    // 这里假设构图黑名单存储的是LensCompositionType枚举
                    compositionBlackList.add(item.toString());
                }
            }
        }



        return new ClothingRuleResult(styleWhiteList, compositionBlackList, usageWhiteList);
    }

    // 为了向后兼容，保留原有的String参数方法
    public static ClothingRuleResult getStyleRules(String firstLevel, String secondLevel) {
        // 根据字符串查找对应的枚举
        ClothFirstCategory primaryCategory = null;
        for (ClothFirstCategory category : ClothFirstCategory.values()) {
            if (category.getCode().equals(firstLevel) || category.getDesc().equals(firstLevel)) {
                primaryCategory = category;
                break;
            }
        }
        
        if (primaryCategory == null) {
            return new ClothingRuleResult(new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        }
        
        Enum<?> secondaryCategory = findSecondaryCategory(primaryCategory, secondLevel);
        if (secondaryCategory == null) {
            return new ClothingRuleResult(new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        }
        
        return getStyleRules(primaryCategory, secondaryCategory);
    }
    
    private static Enum<?> findSecondaryCategory(ClothFirstCategory primaryCategory, String secondLevel) {
        switch (primaryCategory) {
            case 外套:
                for (外套类型 type : 外套类型.values()) {
                    if (type.getCode().equals(secondLevel) || type.getDesc().equals(secondLevel)) {
                        return type;
                    }
                }
                break;
            case 冬季外套:
                for (冬季外套类型 type : 冬季外套类型.values()) {
                    if (type.getCode().equals(secondLevel) || type.getDesc().equals(secondLevel)) {
                        return type;
                    }
                }
                break;
            case 上衣:
                for (上衣类型 type : 上衣类型.values()) {
                    if (type.getCode().equals(secondLevel) || type.getDesc().equals(secondLevel)) {
                        return type;
                    }
                }
                break;
            case 半裙:
                for (半裙类型 type : 半裙类型.values()) {
                    if (type.getCode().equals(secondLevel) || type.getDesc().equals(secondLevel)) {
                        return type;
                    }
                }
                break;
            case 裤子:
                for (裤子类型 type : 裤子类型.values()) {
                    if (type.getCode().equals(secondLevel) || type.getDesc().equals(secondLevel)) {
                        return type;
                    }
                }
                break;
            case 连衣裙:
                for (连衣裙类型 type : 连衣裙类型.values()) {
                    if (type.getCode().equals(secondLevel) || type.getDesc().equals(secondLevel)) {
                        return type;
                    }
                }
                break;
            case 套装:
                for (套装类型 type : 套装类型.values()) {
                    if (type.getCode().equals(secondLevel) || type.getDesc().equals(secondLevel)) {
                        return type;
                    }
                }
                break;
        }
        return null;
    }

    /**
     * 初始化参考图衣服结构白名单
     * 根据服装一级分类和二级分类，定义此类服装可以作为参考图的白名单样式
     */
    private static void initClothStyleWhiteList() {
        // 外套 - 对应表格中的"外套"ClothFirstCategory，包括：大衣、西装外套、风衣、夹克、开衫、新中式外套、其它
        Map<Enum<?>, List<Enum<?>>> outerwear = new HashMap<>();

        // 大衣 -> 大衣/风衣/派克大衣/夹克
        outerwear.put(外套类型.大衣, Arrays.asList(外套类型.大衣, 外套类型.风衣, 冬季外套类型.派克大衣, 外套类型.夹克));
        
        // 西装外套 -> 西装外套/夹克/开衫/西装套装
        outerwear.put(外套类型.西装外套, Arrays.asList(外套类型.西装外套, 外套类型.夹克, 外套类型.开衫, 套装类型.西装套装));
        
        // 风衣 -> 大衣/风衣/派克大衣/夹克
        outerwear.put(外套类型.风衣, Arrays.asList(外套类型.大衣, 外套类型.风衣, 冬季外套类型.派克大衣, 外套类型.夹克));
        
        // 夹克 -> 夹克/开衫/西装外套/休闲套装
        outerwear.put(外套类型.夹克, Arrays.asList(外套类型.夹克, 外套类型.开衫, 外套类型.西装外套, 套装类型.休闲套装));
        
        // 开衫 -> 夹克/开衫/西装外套/休闲套装
        outerwear.put(外套类型.开衫, Arrays.asList(外套类型.夹克, 外套类型.开衫, 外套类型.西装外套, 套装类型.休闲套装));
        
        // 新中式外套 -> 新中式外套/新中式套装
        outerwear.put(外套类型.新中式外套, Arrays.asList(外套类型.新中式外套, 套装类型.新中式套装));
        
        // 其它 -> 外套 (这里需要创建一个通用的外套枚举值或使用字符串)
        outerwear.put(外套类型.其它外套, Arrays.asList(外套类型.其它外套));
        
        CLOTH_REF_WHITE_LIST.put(ClothFirstCategory.外套, outerwear);

        // 冬季外套 - 对应表格中的"冬季外套"ClothFirstCategory，包括：羽绒服、皮草、派克大衣、棉服
        Map<Enum<?>, List<Enum<?>>> winterOuterwear = new HashMap<>();
        
        // 羽绒服 -> 羽绒服/皮草/派克大衣/棉服
        winterOuterwear.put(冬季外套类型.羽绒服, Arrays.asList(冬季外套类型.羽绒服, 冬季外套类型.皮草, 冬季外套类型.派克大衣, 冬季外套类型.棉服));
        
        // 皮草 -> 羽绒服/皮草/派克大衣/棉服
        winterOuterwear.put(冬季外套类型.皮草, Arrays.asList(冬季外套类型.羽绒服, 冬季外套类型.皮草, 冬季外套类型.派克大衣, 冬季外套类型.棉服));
        
        // 派克大衣 -> 羽绒服/皮草/派克大衣/棉服
        winterOuterwear.put(冬季外套类型.派克大衣, Arrays.asList(冬季外套类型.羽绒服, 冬季外套类型.皮草, 冬季外套类型.派克大衣, 冬季外套类型.棉服));
        
        // 棉服 -> 羽绒服/皮草/派克大衣/棉服
        winterOuterwear.put(冬季外套类型.棉服, Arrays.asList(冬季外套类型.羽绒服, 冬季外套类型.皮草, 冬季外套类型.派克大衣, 冬季外套类型.棉服));
        
        CLOTH_REF_WHITE_LIST.put(ClothFirstCategory.冬季外套, winterOuterwear);

        // 上衣 - 对应表格中的"上衣"ClothFirstCategory，包括：新中式上衣、卫衣、毛衣、短袖T恤、吊带、打底衫、衬衫、T恤、POLO衫、其它
        Map<Enum<?>, List<Enum<?>>> tops = new HashMap<>();
        
        // 新中式上衣 -> 新中式上衣/新中式套装
        tops.put(上衣类型.新中式上衣, Arrays.asList(上衣类型.新中式上衣, 套装类型.新中式套装));
        
        // 卫衣 -> 卫衣/毛衣/T恤/短袖T恤/打底衫/衬衫/POLO衫
        tops.put(上衣类型.卫衣, Arrays.asList(上衣类型.卫衣, 上衣类型.毛衣, 上衣类型.T恤, 上衣类型.短袖T恤, 上衣类型.打底衫, 上衣类型.衬衫, 上衣类型.POLO衫));
        
        // 毛衣 -> 卫衣/毛衣/T恤/短袖T恤/打底衫/衬衫/POLO衫
        tops.put(上衣类型.毛衣, Arrays.asList(上衣类型.卫衣, 上衣类型.毛衣, 上衣类型.T恤, 上衣类型.短袖T恤, 上衣类型.打底衫, 上衣类型.衬衫, 上衣类型.POLO衫));
        
        // 短袖T恤 -> 卫衣/毛衣/T恤/短袖T恤/打底衫/衬衫/POLO衫
        tops.put(上衣类型.短袖T恤, Arrays.asList(上衣类型.卫衣, 上衣类型.毛衣, 上衣类型.T恤, 上衣类型.短袖T恤, 上衣类型.打底衫, 上衣类型.衬衫, 上衣类型.POLO衫));
        
        // 吊带 -> 吊带
        tops.put(上衣类型.吊带, Arrays.asList(上衣类型.吊带));
        
        // 打底衫 -> 卫衣/毛衣/T恤/短袖T恤/打底衫/衬衫/POLO衫
        tops.put(上衣类型.打底衫, Arrays.asList(上衣类型.卫衣, 上衣类型.毛衣, 上衣类型.T恤, 上衣类型.短袖T恤, 上衣类型.打底衫, 上衣类型.衬衫, 上衣类型.POLO衫));
        
        // 衬衫 -> 卫衣/毛衣/T恤/短袖T恤/打底衫/衬衫/POLO衫
        tops.put(上衣类型.衬衫, Arrays.asList(上衣类型.卫衣, 上衣类型.毛衣, 上衣类型.T恤, 上衣类型.短袖T恤, 上衣类型.打底衫, 上衣类型.衬衫, 上衣类型.POLO衫));
        
        // T恤 -> 卫衣/毛衣/T恤/短袖T恤/打底衫/衬衫/POLO衫
        tops.put(上衣类型.T恤, Arrays.asList(上衣类型.卫衣, 上衣类型.毛衣, 上衣类型.T恤, 上衣类型.短袖T恤, 上衣类型.打底衫, 上衣类型.衬衫, 上衣类型.POLO衫));
        
        // POLO衫 -> 卫衣/毛衣/T恤/短袖T恤/打底衫/衬衫/POLO衫
        tops.put(上衣类型.POLO衫, Arrays.asList(上衣类型.卫衣, 上衣类型.毛衣, 上衣类型.T恤, 上衣类型.短袖T恤, 上衣类型.打底衫, 上衣类型.衬衫, 上衣类型.POLO衫));
        
        // 其它 -> 其它上衣
        tops.put(上衣类型.其它上衣, Arrays.asList(上衣类型.其它上衣));
        
        CLOTH_REF_WHITE_LIST.put(ClothFirstCategory.上衣, tops);

        // 半裙 - 对应表格中的"半裙"ClothFirstCategory，包括：新中式半裙、牛仔裙、百褶裙、运动裙、短裙、打底裙、A字裙、其它
        Map<Enum<?>, List<Enum<?>>> skirt = new HashMap<>();
        
        // 新中式半裙 -> 新中式半裙/新中式套装/新中式连衣裙
        skirt.put(半裙类型.新中式半裙, Arrays.asList(半裙类型.新中式半裙, 套装类型.新中式套装, 连衣裙类型.新中式连衣裙));
        
        // 牛仔裙 -> 牛仔裙/运动裙/短裙/A字裙/打底裙/百褶裙
        skirt.put(半裙类型.牛仔裙, Arrays.asList(半裙类型.牛仔裙, 半裙类型.运动裙, 半裙类型.短裙, 半裙类型.A字裙, 半裙类型.打底裙, 半裙类型.百褶裙));
        
        // 百褶裙 -> 牛仔裙/运动裙/短裙/A字裙/打底裙/百褶裙
        skirt.put(半裙类型.百褶裙, Arrays.asList(半裙类型.牛仔裙, 半裙类型.运动裙, 半裙类型.短裙, 半裙类型.A字裙, 半裙类型.打底裙, 半裙类型.百褶裙));
        
        // 运动裙 -> 牛仔裙/运动裙/短裙/A字裙/打底裙/百褶裙
        skirt.put(半裙类型.运动裙, Arrays.asList(半裙类型.牛仔裙, 半裙类型.运动裙, 半裙类型.短裙, 半裙类型.A字裙, 半裙类型.打底裙, 半裙类型.百褶裙));
        
        // 短裙 -> 牛仔裙/运动裙/短裙/A字裙/打底裙/百褶裙
        skirt.put(半裙类型.短裙, Arrays.asList(半裙类型.牛仔裙, 半裙类型.运动裙, 半裙类型.短裙, 半裙类型.A字裙, 半裙类型.打底裙, 半裙类型.百褶裙));
        
        // 打底裙 -> 牛仔裙/运动裙/短裙/A字裙/打底裙/百褶裙
        skirt.put(半裙类型.打底裙, Arrays.asList(半裙类型.牛仔裙, 半裙类型.运动裙, 半裙类型.短裙, 半裙类型.A字裙, 半裙类型.打底裙, 半裙类型.百褶裙));
        
        // A字裙 -> 牛仔裙/运动裙/短裙/A字裙/打底裙/百褶裙
        skirt.put(半裙类型.A字裙, Arrays.asList(半裙类型.牛仔裙, 半裙类型.运动裙, 半裙类型.短裙, 半裙类型.A字裙, 半裙类型.打底裙, 半裙类型.百褶裙));
        
        // 其它 -> 其它半裙
        skirt.put(半裙类型.其它半裙, Arrays.asList(半裙类型.其它半裙));
        
        CLOTH_REF_WHITE_LIST.put(ClothFirstCategory.半裙, skirt);

        // 裤子 - 对应表格中的"裤子"ClothFirstCategory，包括：运动裤、西装裤、牛仔裤、工装裤、短裤、登山裤、打底裤、其它
        Map<Enum<?>, List<Enum<?>>> pants = new HashMap<>();
        
        // 运动裤 -> 运动裤/西装裤/牛仔裤/工装裤/短裤/登山裤/打底裤
        pants.put(裤子类型.运动裤,
                Arrays.asList(裤子类型.运动裤, 裤子类型.西装裤, 裤子类型.牛仔裤, 裤子类型.工装裤, 裤子类型.短裤, 裤子类型.登山裤,
                        裤子类型.打底裤));
        
        // 西装裤 -> 运动裤/西装裤/牛仔裤/工装裤/短裤/登山裤/打底裤
        pants.put(裤子类型.西装裤,
                Arrays.asList(裤子类型.运动裤, 裤子类型.西装裤, 裤子类型.牛仔裤, 裤子类型.工装裤, 裤子类型.短裤, 裤子类型.登山裤,
                        裤子类型.打底裤));
        
        // 牛仔裤 -> 运动裤/西装裤/牛仔裤/工装裤/短裤/登山裤/打底裤
        pants.put(裤子类型.牛仔裤, Arrays.asList(裤子类型.运动裤, 裤子类型.西装裤, 裤子类型.牛仔裤, 裤子类型.工装裤, 裤子类型.短裤, 裤子类型.登山裤,
                裤子类型.打底裤));
        
        // 工装裤 -> 运动裤/西装裤/牛仔裤/工装裤/短裤/登山裤/打底裤
        pants.put(裤子类型.工装裤,
                Arrays.asList(裤子类型.运动裤, 裤子类型.西装裤, 裤子类型.牛仔裤, 裤子类型.工装裤, 裤子类型.短裤, 裤子类型.登山裤,
                        裤子类型.打底裤));
        
        // 短裤 -> 运动裤/西装裤/牛仔裤/工装裤/短裤/登山裤/打底裤
        pants.put(裤子类型.短裤, Arrays.asList(裤子类型.运动裤, 裤子类型.西装裤, 裤子类型.牛仔裤, 裤子类型.工装裤, 裤子类型.短裤, 裤子类型.登山裤,
                裤子类型.打底裤));
        
        // 登山裤 -> 运动裤/西装裤/牛仔裤/工装裤/短裤/登山裤/打底裤
        pants.put(裤子类型.登山裤,
                Arrays.asList(裤子类型.运动裤, 裤子类型.西装裤, 裤子类型.牛仔裤, 裤子类型.工装裤, 裤子类型.短裤, 裤子类型.登山裤,
                        裤子类型.打底裤));
        
        // 打底裤 -> 运动裤/西装裤/牛仔裤/工装裤/短裤/登山裤/打底裤
        pants.put(裤子类型.打底裤,
                Arrays.asList(裤子类型.运动裤, 裤子类型.西装裤, 裤子类型.牛仔裤, 裤子类型.工装裤, 裤子类型.短裤, 裤子类型.登山裤,
                        裤子类型.打底裤));
        
        // 其它 -> 其它裤子
        pants.put(裤子类型.其它裤子, Arrays.asList(裤子类型.其它裤子));
        
        CLOTH_REF_WHITE_LIST.put(ClothFirstCategory.裤子, pants);

        // 连衣裙 - 对应表格中的"连衣裙"ClothFirstCategory，包括：新中式连衣裙、公主裙、其它
        Map<Enum<?>, List<Enum<?>>> dress = new HashMap<>();
        
        // 新中式连衣裙 -> 新中式连衣裙/新中式套装
        dress.put(连衣裙类型.新中式连衣裙, Arrays.asList(连衣裙类型.新中式连衣裙, 套装类型.新中式套装));
        
        // 公主裙 -> 公主裙
        dress.put(连衣裙类型.公主裙, Arrays.asList(连衣裙类型.公主裙));
        
        // 其它 -> 其它连衣裙
        dress.put(连衣裙类型.其它连衣裙, Arrays.asList(连衣裙类型.其它连衣裙));
        
        CLOTH_REF_WHITE_LIST.put(ClothFirstCategory.连衣裙, dress);

        // 套装 - 对应表格中的"套装"ClothFirstCategory，包括：职业套装、西装套装、休闲套装、新中式套装、汉服、其它
        Map<Enum<?>, List<Enum<?>>> suit = new HashMap<>();
        
        // 职业套装 -> 职业套装/西装套装
        suit.put(套装类型.职业套装, Arrays.asList(套装类型.职业套装, 套装类型.西装套装));
        
        // 西装套装 -> 职业套装/西装套装
        suit.put(套装类型.西装套装, Arrays.asList(套装类型.职业套装, 套装类型.西装套装));
        
        // 休闲套装 -> 休闲套装
        suit.put(套装类型.休闲套装, Arrays.asList(套装类型.休闲套装));
        
        // 新中式套装 -> 新中式套装/新中式上衣/新中式半裙/新中式连衣裙/汉服
        suit.put(套装类型.新中式套装, Arrays.asList(套装类型.新中式套装, 上衣类型.新中式上衣, 半裙类型.新中式半裙, 连衣裙类型.新中式连衣裙, 套装类型.汉服));
        
        // 汉服 -> 新中式套装/汉服
        suit.put(套装类型.汉服, Arrays.asList(套装类型.新中式套装, 套装类型.汉服));
        
        // 其它 -> 其它套装
        suit.put(套装类型.其它套装, Arrays.asList(套装类型.其它套装));
        
        CLOTH_REF_WHITE_LIST.put(ClothFirstCategory.套装, suit);
    }

    /**
     * 初始化构图黑名单
     * 根据表格中的"构图黑名单"列，定义不适合的构图方式：
     * - 上半身类服装（外套、冬季外套、上衣）：不适合 lower-body close up
     * - 下半身类服装（半裙、裤子）：不适合 upper-body close up
     */
    private static void initClothCompositionBlackList() {
        // 外套 - 上半身服装，不适合下半身特写 (lower-body close up)
        Map<Enum<?>, List<Enum<?>>> outerwear = new HashMap<>();
        outerwear.put(外套类型.大衣, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        outerwear.put(外套类型.夹克, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        outerwear.put(外套类型.风衣, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        outerwear.put(外套类型.西装外套, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        outerwear.put(外套类型.开衫, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        outerwear.put(外套类型.新中式外套, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        outerwear.put(外套类型.其它外套, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        CLOTH_COMPOSITION_BLACK_LIST.put(ClothFirstCategory.外套, outerwear);

        // 冬季外套 - 上半身服装，不适合下半身特写 (lower-body close up)
        Map<Enum<?>, List<Enum<?>>> winterOuterwear = new HashMap<>();
        winterOuterwear.put(冬季外套类型.羽绒服, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        winterOuterwear.put(冬季外套类型.皮草, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        winterOuterwear.put(冬季外套类型.派克大衣, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        winterOuterwear.put(冬季外套类型.棉服, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        CLOTH_COMPOSITION_BLACK_LIST.put(ClothFirstCategory.冬季外套, winterOuterwear);

        // 上衣 - 上半身服装，不适合下半身特写 (lower-body close up)
        Map<Enum<?>, List<Enum<?>>> tops = new HashMap<>();
        tops.put(上衣类型.新中式上衣, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        tops.put(上衣类型.卫衣, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        tops.put(上衣类型.毛衣, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        tops.put(上衣类型.短袖T恤, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        tops.put(上衣类型.吊带, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        tops.put(上衣类型.打底衫, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        tops.put(上衣类型.衬衫, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        tops.put(上衣类型.T恤, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        tops.put(上衣类型.POLO衫, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        tops.put(上衣类型.其它上衣, Arrays.asList(LensCompositionType.LOWER_BODY_CLOSE_UP));
        CLOTH_COMPOSITION_BLACK_LIST.put(ClothFirstCategory.上衣, tops);

        // 半裙 - 下半身服装，不适合上半身特写 (upper-body close up) 和胸部特写
        Map<Enum<?>, List<Enum<?>>> skirt = new HashMap<>();
        skirt.put(半裙类型.新中式半裙, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        skirt.put(半裙类型.牛仔裙, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        skirt.put(半裙类型.百褶裙, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        skirt.put(半裙类型.运动裙, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        skirt.put(半裙类型.短裙, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        skirt.put(半裙类型.打底裙, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        skirt.put(半裙类型.A字裙, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        skirt.put(半裙类型.其它半裙, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        CLOTH_COMPOSITION_BLACK_LIST.put(ClothFirstCategory.半裙, skirt);

        // 裤子 - 下半身服装，不适合上半身特写 (upper-body close up) 和胸部特写
        Map<Enum<?>, List<Enum<?>>> pants = new HashMap<>();
        pants.put(裤子类型.运动裤, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        pants.put(裤子类型.西装裤, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        pants.put(裤子类型.牛仔裤, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        pants.put(裤子类型.工装裤, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        pants.put(裤子类型.短裤, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        pants.put(裤子类型.登山裤, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        pants.put(裤子类型.打底裤, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        pants.put(裤子类型.其它裤子, Arrays.asList(LensCompositionType.UPPER_BODY_CLOSE_UP, LensCompositionType.BUST_SHOT));
        CLOTH_COMPOSITION_BLACK_LIST.put(ClothFirstCategory.裤子, pants);

        // 连衣裙 - 全身服装，适合各种构图，暂无黑名单
        Map<Enum<?>, List<Enum<?>>> dress = new HashMap<>();
        dress.put(连衣裙类型.新中式连衣裙, Arrays.asList());
        dress.put(连衣裙类型.公主裙, Arrays.asList());
        dress.put(连衣裙类型.其它连衣裙, Arrays.asList());
        CLOTH_COMPOSITION_BLACK_LIST.put(ClothFirstCategory.连衣裙, dress);

        // 套装 - 全身服装，适合各种构图，暂无黑名单
        Map<Enum<?>, List<Enum<?>>> suit = new HashMap<>();
        suit.put(套装类型.职业套装, Arrays.asList());
        suit.put(套装类型.西装套装, Arrays.asList());
        suit.put(套装类型.休闲套装, Arrays.asList());
        suit.put(套装类型.新中式套装, Arrays.asList());
        suit.put(套装类型.汉服, Arrays.asList());
        suit.put(套装类型.其它套装, Arrays.asList());
        CLOTH_COMPOSITION_BLACK_LIST.put(ClothFirstCategory.套装, suit);
    }

    /**
     * 初始化服装用途白名单
     * 根据prompt中的Purpose-Based Classification，定义功能性服装类型
     */

}