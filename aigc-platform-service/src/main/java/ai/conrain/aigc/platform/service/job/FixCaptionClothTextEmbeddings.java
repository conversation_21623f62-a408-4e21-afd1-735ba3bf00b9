package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.example.ImageCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.onnx.GenreRecognizeOnnxService;
import ai.conrain.aigc.platform.service.component.onnx.ImageQualityOnnxService;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.pgvector.PGvector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class FixCaptionClothTextEmbeddings extends JavaProcessor {

    @Autowired
    private ImageCaptionDAO imageCaptionDAO;

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageCaptionService imageCaptionService;

    @Autowired
    private ImageQualityOnnxService imageQualityOnnxService;

    @Autowired
    private GenreRecognizeOnnxService genreRecognizeOnnxService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        try {
            ImageCaptionExample exam = new ImageCaptionExample();
            exam.createCriteria().andImageTypeEqualTo("scene").andCaptionIsNotNull().andClothTextEmbIsNull();
//            exam.setRows(250);
            exam.setOrderByClause("id desc");

            List<ImageCaptionDO> imageCaptionDOS = imageCaptionDAO.selectSimpleByExample(exam);

            for (ImageCaptionDO imageCaptionDO : imageCaptionDOS) {
                try {
                    if (imageCaptionDO.getCaption() == null || !CommonUtil.isValidJson(imageCaptionDO.getCaption())) {
                        continue;
                    }

                    ImageAnalysisCaption caption = JSONObject.parseObject(imageCaptionDO.getCaption(), ImageAnalysisCaption.class);

                    // 服装描述文本向量（使用多模态api计算，且维度为1024，api和维度，需要与阿九保持一致）
                    if (caption.getClothing() != null) {
                        PGvector clothVector = imageCaptionService.calcClothTextVector(caption, ClothTypeEnum.TwoPiece);

                        ImageCaptionVO target = new ImageCaptionVO();
                        target.setId(imageCaptionDO.getId());
                        target.setClothTextEmb(clothVector);
                        imageCaptionService.updateByIdSelective(target);
                    }

                } catch (Exception e) {
                    log.error("fix caption recall embeddings failed for caption id:{},ignored", imageCaptionDO.getId(), e);
                }
            }
        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }
}
