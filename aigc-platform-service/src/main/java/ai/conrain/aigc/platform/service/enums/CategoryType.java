package ai.conrain.aigc.platform.service.enums;

/**
 * 分类类型枚举
 * 用于定义各种分类的键名常量，避免硬编码字符串
 */
public enum CategoryType {
    /**
     * 上装二级分类
     */
    TOP_CATE_2("top_cate_2"),

    /**
     * 下装二级分类
     */
    BOTTOM_CATE_2("bottom_cate_2"),

    /**
     * 连体服装二级分类
     */
    ONEPIECE_CATE_2("onepiece_cate_2"),

    /**
     * 套装二级分类
     */
    SUIT_CATE_2("suit_cate_2"),


    /**
     * 用途一级分类
     */
    USAGE_CATE_1("usage_cate_1"),

    /**
     * 用途二级分类
     */
    USAGE_CATE_2("usage_cate_2"),

    /**
     * 构图分类
     */
    COMPOSITION("composition");

    private final String key;

    CategoryType(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    @Override
    public String toString() {
        return key;
    }
}