package ai.conrain.aigc.platform.service.model.vo;

import com.alibaba.fastjson2.JSONObject;
import java.util.List;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * ImageGroupVO
 *
 * @version ImageGroupService.java v 0.1 2025-07-30 08:19:30
 */
@Data
public class ImageGroupVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 主键 */
	private Integer id;

	/** 图片ID列表 */
	private List<Integer> imageIds;

    /** 图像元数据，JSON格式 */
    private JSONObject metadata;

    /** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

}
