package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

/**
 * 服装用途子分类枚举
 */
@Getter
public enum UsageSubCategoryEnum {
    // Functional 子分类
    MATERNITY_WEAR("Maternity_Wear", "孕妇装", UsageMainCategoryEnum.FUNCTIONAL),
    RAINCOAT("Raincoat", "雨衣", UsageMainCategoryEnum.FUNCTIONAL),
    DANCE_WEAR("Dance_Wear", "舞蹈服", UsageMainCategoryEnum.FUNCTIONAL),
    COSPLAY_COSTUME("Cosplay_Costume", "cosplay", UsageMainCategoryEnum.FUNCTIONAL),
    
    // Outdoor_Wear 子分类
    OUTDOOR_OTHERS("Others", "其它", UsageMainCategoryEnum.OUTDOOR_WEAR),
    SUN_PROTECTION("Sun_Protection", "防晒服", UsageMainCategoryEnum.OUTDOOR_WEAR),
    SHELL_JACKET("Shell_Jacket", "冲锋衣", UsageMainCategoryEnum.OUTDOOR_WEAR),
    
    // Sportswear 子分类
    YOGA("Yoga", "瑜伽", UsageMainCategoryEnum.SPORTSWEAR),
    SPORTSWEAR_OTHERS("Others", "其它", UsageMainCategoryEnum.SPORTSWEAR);

    private final String code;
    private final String desc;
    private final UsageMainCategoryEnum mainCategory;

    UsageSubCategoryEnum(String code, String desc, UsageMainCategoryEnum mainCategory) {
        this.code = code;
        this.desc = desc;
        this.mainCategory = mainCategory;
    }

    public static UsageSubCategoryEnum getByCode(String code) {
        for (UsageSubCategoryEnum subCategory : values()) {
            if (subCategory.getCode().equals(code)) {
                return subCategory;
            }
        }
        return null;
    }

    /**
     * 根据主分类和子分类代码获取枚举
     */
    public static UsageSubCategoryEnum getByMainCategoryAndCode(UsageMainCategoryEnum mainCategory, String code) {
        for (UsageSubCategoryEnum subCategory : values()) {
            if (subCategory.getMainCategory() == mainCategory && subCategory.getCode().equals(code)) {
                return subCategory;
            }
        }
        return null;
    }
}