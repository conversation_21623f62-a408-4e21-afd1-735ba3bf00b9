/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static ai.conrain.aigc.platform.service.enums.PointLogTypeEnum.BASIC_CHANGING_CLOTHES_RETURN;

/**
 * 创作类型
 *
 * <AUTHOR>
 * @version : CreativeTypeEnum.java, v 0.1 2024/7/4 19:48 renxiao.wu Exp $
 */
@Getter
public enum CreativeTypeEnum {
    CREATE_IMAGE("CREATE_IMAGE", "图片创作", new BigDecimal("0.4"), SystemConstants.CREATIVE_FLOW_PARAMS,
        new ElementConfigKeyEnum[] {ElementConfigKeyEnum.FACE, ElementConfigKeyEnum.SCENE},
        new ElementConfigKeyEnum[] {ElementConfigKeyEnum.FACE, ElementConfigKeyEnum.SCENE},
        PointLogTypeEnum.CREATIVE_CREATE, PointLogTypeEnum.CREATIVE_RETURN, null, false),

    //（自研）try on
    TRYON("TRYON", "虚拟试衣", BigDecimal.ZERO, SystemConstants.TRYON_COMFYUI_WORKFLOW, null, null, null, null, null,
        false),

    LOGO_COMBINE("LOGO_COMBINE", "印花上身", new BigDecimal("0.5"), SystemConstants.LOGO_COMBINE_FLOW_PARAMS,
        new ElementConfigKeyEnum[] {ElementConfigKeyEnum.CLOTH_STYLE, ElementConfigKeyEnum.REFER,
                                    ElementConfigKeyEnum.FACE},
        new ElementConfigKeyEnum[] {ElementConfigKeyEnum.CLOTH_STYLE, ElementConfigKeyEnum.REFER},
        PointLogTypeEnum.LOGO_COMBINE_CREATE, PointLogTypeEnum.LOGO_COMBINE_RETURN, null, false),

    CREATE_VIDEO("CREATE_VIDEO", "视频创作", new BigDecimal("12.5"), null, null, null,
        PointLogTypeEnum.CREATE_VIDEO_CREATE, PointLogTypeEnum.CREATE_VIDEO_RETURN, null, true, true),

    FIX_VIDEO_FACE("FIX_VIDEO_FACE", "视频修脸", BigDecimal.ZERO, SystemConstants.FIX_VIDEO_FACE_FLOW_PARAMS,
        new ElementConfigKeyEnum[] {ElementConfigKeyEnum.FACE}, null, null, null, "v_f", false, true),

    // 图片抠图--基础款换衣
    PICTURE_MATTING("PICTURE_MATTING", "图片抠图", BigDecimal.ZERO, SystemConstants.PICTURE_MATTING_COMFYUI_WORKFLOW,
        null, null, null, null, null, false),

    // 基础款换衣
    BASIC_CHANGING_CLOTHES("BASIC_CHANGING_CLOTHES", "基础款换衣", new BigDecimal("1.2"),
        SystemConstants.BASIC_CHANGING_CLOTHES_FLOW_PARAMS,
        new ElementConfigKeyEnum[] {ElementConfigKeyEnum.FACE, ElementConfigKeyEnum.SCENE}, null,
        PointLogTypeEnum.BASIC_CHANGING_CLOTHES, BASIC_CHANGING_CLOTHES_RETURN, null, false, false),
    // 绘蛙
    HUIWA_BASIC_CHANGING_CLOTHES("HUIWA_BASIC_CHANGING_CLOTHES", "绘蛙基础款换衣", new BigDecimal("0"),
        SystemConstants.BASIC_CHANGING_CLOTHES_FLOW_PARAMS,
        new ElementConfigKeyEnum[] {ElementConfigKeyEnum.FACE, ElementConfigKeyEnum.SCENE}, null,
        PointLogTypeEnum.HUIWA_BASIC_CHANGING_CLOTHES, null, null, false, false),

    //服装自动分割（服装换色）
    CLOTH_AUTO_SEGMENT("CLOTH_AUTO_SEGMENT", "服装自动分割", BigDecimal.ZERO,
        SystemConstants.CLOTH_AUTO_SEGMENT_WORKFLOW, null, null, null, null,
        null, false, false, false, BigDecimal.ZERO, false, false),

    // 姿势示例图
    POSE_SAMPLE_DIAGRAM("POSE_SAMPLE_DIAGRAM", "姿势示例图", BigDecimal.ZERO,
        SystemConstants.POSE_SAMPLE_DIAGRAM_FLOW_PARAMS,
        new ElementConfigKeyEnum[] {ElementConfigKeyEnum.FACE, ElementConfigKeyEnum.SCENE}, null,
        PointLogTypeEnum.POSE_SAMPLE_DIAGRAM, null, null, false, false),

    FACE_PINCHING("FACE_PINCHING", "模特捏脸", BigDecimal.ZERO, SystemConstants.FACE_PINCHING_FLOW, null, null, null,
        null, "f_p", false, false),

    // 固定姿势创作
    FIXED_POSTURE_CREATION("FIXED_POSTURE_CREATION", "固定姿势创作", new BigDecimal("0.4"),
        SystemConstants.FIXED_POSTURE_CREATION_FLOW,
        new ElementConfigKeyEnum[] {ElementConfigKeyEnum.FACE, ElementConfigKeyEnum.SCENE}, null,
        PointLogTypeEnum.FIXED_POSTURE_CREATION_CREATE, PointLogTypeEnum.FIXED_POSTURE_CREATION_RETURN, null, false),

    // 使用细节修补流程大牌上身
    BRAND_TRY_ON("BRAND_TRY_ON", "使用细节修补流程大牌上身", new BigDecimal("0"), SystemConstants.BRAND_TRY_ON_FLOW,
        null, null, PointLogTypeEnum.BRAND_TRY_ON_CREATE, PointLogTypeEnum.BRAND_TRY_ON_RETURN,
        null, false, false, false, new BigDecimal("0.4"), true, false),

    FACE_SCENE_SWITCH("FACE_SCENE_SWITCH", "换头换背景", new BigDecimal("0.4"),
        SystemConstants.FACE_SCENE_SWITCH_FLOW_PARAMS,
        new ElementConfigKeyEnum[] {ElementConfigKeyEnum.FACE, ElementConfigKeyEnum.SCENE},
        new ElementConfigKeyEnum[] {}, PointLogTypeEnum.FACE_SCENE_SWITCH_CREATE,
        PointLogTypeEnum.FACE_SCENE_SWITCH_CREATE,
        null, false, false, false, new BigDecimal("0.4"), true, false),

    /**
     * AI 小工具
     */
    // 细节修补
    REPAIR_DETAIL("REPAIR_DETAIL", "细节修补", new BigDecimal("0"), SystemConstants.REPAIR_DETAIL_FLOW_PARAMS, null,
        null, PointLogTypeEnum.PARTIAL_REDRAW_CREATE, PointLogTypeEnum.REPAIR_DETAIL_CREATE,
        null, false, false, false, new BigDecimal("0.4"), false, false),
    // 局部重绘
    PARTIAL_REDRAW("PARTIAL_REDRAW", "局部重绘", new BigDecimal("0"), SystemConstants.PARTIAL_REDRAW_FLOW_PARAMS,
        null, null, PointLogTypeEnum.PARTIAL_REDRAW_CREATE, PointLogTypeEnum.PARTIAL_REDRAW_CREATE,
        null, false, false, false, new BigDecimal("0.4"), false, false),
    // 手部修复
    REPAIR_HANDS("REPAIR_HANDS", "手部修复", new BigDecimal("0"), SystemConstants.REPAIR_HANDS_FLOW_PARAMS, null,
        null, PointLogTypeEnum.REPAIR_HANDS_CREATE, null,
        null, false, false, false, new BigDecimal("0.4"), false, false),

    // 衣服去皱
    REMOVE_WRINKLE("REMOVE_WRINKLE", "衣服去皱", new BigDecimal("0"), SystemConstants.CR_UPSCALE_FLOW_PARAMS, null,
        null, PointLogTypeEnum.REMOVE_WRINKLE_CREATE, null,
        null, false, false, true, new BigDecimal("0.4"), false, false),

    // 羽绒服去皱
    REMOVE_WRINKLE_4_DC("REMOVE_WRINKLE_4_DC", "羽绒服去皱", new BigDecimal("0.6"), null, null, null,
        PointLogTypeEnum.REMOVE_WRINKLE_4_DC_CREATE, PointLogTypeEnum.REMOVE_WRINKLE_4_DC_RETURN,
        null, false, false, true, new BigDecimal("0.6"), false, false),

    // 消除笔
    ERASE_BRUSH("ERASE_BRUSH", "消除笔", new BigDecimal("0"), SystemConstants.ERASE_BRUSH_FLOW_PARAMS, null, null,
        PointLogTypeEnum.ERASE_BRUSH_CREATE, null,
        null, false, false, false, new BigDecimal("0"), false, false),
    // 图片放大
    IMAGE_UPSCALE("IMAGE_UPSCALE", "图片放大", new BigDecimal("0.4"), SystemConstants.IMAGE_UPSCALE_FLOW_PARAMS, null,
        null, PointLogTypeEnum.IMAGE_UPSCALE_CREATE, PointLogTypeEnum.IMAGE_UPSCALE_RETURN, "i_upscale", false, false,
        false, new BigDecimal("0.4"), false, false),

    ERASE_BRUSH_V2("ERASE_BRUSH_V2", "消除笔", new BigDecimal("0"), null, null, null,
        PointLogTypeEnum.ERASE_BRUSH_V2_CREATE, PointLogTypeEnum.ERASE_BRUSH_V2_RETURN, null, true, false, false,
        new BigDecimal("0"),
        false, false),

    //服装换色
    CLOTH_RECOLOR("CLOTH_RECOLOR", "服装换色", new BigDecimal("0.4"), SystemConstants.CLOTH_AUTO_SEGMENT_WORKFLOW, null,
        null, PointLogTypeEnum.CLOTH_RECOLOR, null, null,
        false, false, false, new BigDecimal("0.4"), false, false),

    TEST_CREATIVE_FLOW("TEST_CREATIVE_FLOW", "测试创作流程", BigDecimal.ZERO, SystemConstants.CREATIVE_FLOW_PARAMS,
        null, null, null, null, "t_c", false),

    ;

    /**
     * 枚举码
     */
    private final String code;

    /**
     * 枚举描述
     */
    private final String desc;

    /**
     * 消耗的缪斯点数量
     */
    private final BigDecimal consumeMusePoints;

    /**
     * 上传消耗的缪斯点数量
     */
    private final BigDecimal consumeMusePointsForUpload;

    /**
     * 流程名称
     */
    private final String flowKey;

    /**
     * 元素必传配置
     */
    private final ElementConfigKeyEnum[] configKeys;

    /**
     * 需要校验的元素配置
     */
    private final ElementConfigKeyEnum[] validateConfigKeys;

    private final PointLogTypeEnum logType;

    private final PointLogTypeEnum returnLogType;

    /**
     * 生成图片的额外标记，默认为空不加标记
     */
    private final String imageTag;

    /**
     * 是否手动模式
     */
    private boolean manual = false;

    private boolean videoCreative = false;

    /**
     * 是否外部调用
     */
    private boolean external = false;

    /**
     * 是否基于图片张数计费
     */
    private boolean consumeByImageCnt = true;

    /**
     * 是否需要内容安全检查
     */
    private boolean needModerate = true;

    /**
     * 手动模式类型列表
     */
    private static final List<String> MANUAL_TYPES = new ArrayList<>();

    /**
     * 手动模式类型列表
     */
    private static final List<String> SHOW_POINT_LOG_TYPE_LIST = new ArrayList<>();

    /**
     * 外部调用类型列表
     */
    private static final List<String> EXTERNAL_TYPES = new ArrayList<>();

    /**
     * 图片抠图类型列表
     */
    private static final List<String> PICTURE_MATTING_LIST = new ArrayList<>();

    /**
     * 三方应用任务
     */
    private static final List<String> THIRD_PART_LIST = new ArrayList<>();

    static {
        for (CreativeTypeEnum item : values()) {
            if (item.isManual()) {
                MANUAL_TYPES.add(item.getCode());
            }

            if (item.getLogType() != null) {
                SHOW_POINT_LOG_TYPE_LIST.add(item.getLogType().getCode());
            }

            if (item.isExternal()) {
                EXTERNAL_TYPES.add(item.getCode());
            }
        }

        PICTURE_MATTING_LIST.add(PICTURE_MATTING.getCode());
        PICTURE_MATTING_LIST.add(FACE_PINCHING.getCode());
        PICTURE_MATTING_LIST.add(CLOTH_AUTO_SEGMENT.getCode());

        // 完善过程类型列表
        THIRD_PART_LIST.add(HUIWA_BASIC_CHANGING_CLOTHES.getCode());
    }

    CreativeTypeEnum(String code, String desc, BigDecimal consumeMusePoints, String flowKey,
                     ElementConfigKeyEnum[] configKeys, ElementConfigKeyEnum[] validateConfigKeys,
                     PointLogTypeEnum logType, PointLogTypeEnum returnLogType, String imageTag, boolean manual) {
        this(code, desc, consumeMusePoints, flowKey, configKeys, validateConfigKeys, logType, returnLogType, imageTag,
            manual, false);
    }

    CreativeTypeEnum(String code, String desc, BigDecimal consumeMusePoints, String flowKey,
                     ElementConfigKeyEnum[] configKeys, ElementConfigKeyEnum[] validateConfigKeys,
                     PointLogTypeEnum logType, PointLogTypeEnum returnLogType, String imageTag, boolean manual,
                     boolean videoCreative) {
        this(code, desc, consumeMusePoints, flowKey, configKeys, validateConfigKeys, logType, returnLogType, imageTag,
            manual, videoCreative, false, null, true, true);
    }

    CreativeTypeEnum(String code, String desc, BigDecimal consumeMusePoints, String flowKey,
                     ElementConfigKeyEnum[] configKeys, ElementConfigKeyEnum[] validateConfigKeys,
                     PointLogTypeEnum logType, PointLogTypeEnum returnLogType, String imageTag, boolean manual,
                     boolean videoCreative, boolean external, BigDecimal consumeMusePointsForUpload,
                     boolean consumeByImageCnt, boolean needModerate) {
        this.code = code;
        this.desc = desc;
        this.consumeMusePoints = consumeMusePoints;
        this.flowKey = flowKey;
        this.configKeys = configKeys;
        this.validateConfigKeys = validateConfigKeys;
        this.logType = logType;
        this.returnLogType = returnLogType;
        this.imageTag = imageTag;
        this.manual = manual;
        this.videoCreative = videoCreative;
        this.external = external;
        this.consumeMusePointsForUpload = consumeMusePointsForUpload;
        this.consumeByImageCnt = consumeByImageCnt;
        this.needModerate = needModerate;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static CreativeTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (CreativeTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }

    /**
     * 获取手动模式的类型列表
     *
     * @return 手动模式的类型列表
     */
    public static List<String> getManualTypes() {
        return MANUAL_TYPES;
    }

    /**
     * 获取需要展示的算力流水类型列表
     *
     * @return 需要展示的算力流水类型列表
     */
    public static List<String> getShowPointLogTypeList() {
        return SHOW_POINT_LOG_TYPE_LIST;
    }

    /**
     * 获取外部调用的创作类型
     *
     * @return list
     */
    public static List<String> getExternalTypes() {
        return EXTERNAL_TYPES;
    }

    /**
     * 图片抠图的类型列表
     *
     * @return list
     */
    public static List<String> getPictureMattingTypeList() {
        return PICTURE_MATTING_LIST;
    }

    /**
     * 过程类型列表
     *
     * @return list
     */
    public static List<String> getThirdPartList() {
        return THIRD_PART_LIST;
    }

    public static List<String> getAllWithout(List<String> types) {
        Stream<String> all = Arrays.stream(values()).map(CreativeTypeEnum::getCode);
        if (CollectionUtils.isEmpty(types)) {
            return all.collect(Collectors.toList());
        }
        return all.filter(itemCode -> !types.contains(itemCode)).collect(Collectors.toList());
    }

    /**
     * 上传时消耗的缪斯点数
     *
     * @return 缪斯点数
     */
    public BigDecimal getConsumeMusePointsForUpload() {
        if (consumeMusePointsForUpload == null) {
            return consumeMusePoints;
        }
        return consumeMusePointsForUpload;
    }

    /**
     * 获取消耗的赠送点数, consumeMusePoints * 2.5
     */
    public BigDecimal getConsumeGivePoint() {
        if (consumeMusePoints == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimalUtils.multiply(consumeMusePoints, CommonConstants.MUSE_TO_GIVE_COEFFICIENT);
    }

    /**
     * 上传时消耗的赠送点, consumeMusePointsForUpload * 2.5
     */
    public BigDecimal getConsumeGivePointForUpload() {
        if (consumeMusePointsForUpload == null) {
            return getConsumeGivePoint();
        }
        return BigDecimalUtils.multiply(consumeMusePointsForUpload, CommonConstants.MUSE_TO_GIVE_COEFFICIENT);
    }

    public boolean free() {
        return BigDecimalUtils.equalsZero(getConsumeMusePoints())
               && BigDecimalUtils.equalsZero(getConsumeMusePointsForUpload())
               && BigDecimalUtils.equalsZero(getConsumeGivePoint())
               && BigDecimalUtils.equalsZero(getConsumeGivePointForUpload());
    }
}
