package ai.conrain.aigc.platform.service.util;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;
import org.jetbrains.annotations.NotNull;

/**
 * 通用图片信息读取工具（仅读取宽高和大小，不加载像素）
 * 支持格式：JPEG、PNG、GIF、BMP
 */
public class ImageInfoUtil {

    public record ImageInfo(int width, int height, int size) {

        @NotNull
        @Override
        public String toString() {
            return String.format("ImageInfo{width=%d, height=%d, size=%d}", width, height, size);
        }
    }

    /**
     * 获取图片的宽度、高度和原始数据大小（字节）
     *
     * @param imageBytes 图片字节数组
     * @return ImageInfo 对象，失败返回 null
     */
    public static ImageInfo getImageInfo(byte[] imageBytes) {
        if (imageBytes == null || imageBytes.length < 10) {
            return null;
        }

        try (ImageInputStream iis = ImageIO.createImageInputStream(new ByteArrayInputStream(imageBytes))) {
            Iterator<ImageReader> readers = ImageIO.getImageReaders(iis);
            if (!readers.hasNext()) {
                return null;
            }

            ImageReader reader = readers.next();
            try {
                reader.setInput(iis, true); // true: use strict mode
                int width = reader.getWidth(0);
                int height = reader.getHeight(0);
                return new ImageInfo(width, height, imageBytes.length);
            } catch (Exception e) {
                // 根据魔数（Magic Number）判断格式
                String format = detectFormat(imageBytes);
                if (format == null) {
                    return null;
                }
                // 可能是损坏的图片或特殊编码（如 CMYK），尝试 fallback
                return fallbackParse(imageBytes, format);
            } finally {
                reader.dispose();
            }
        } catch (IOException e) {
            return null;
        }
    }

    /**
     * 根据前几个字节判断图片格式
     */
    private static String detectFormat(byte[] bytes) {
        if (bytes[0] == (byte)0xFF && bytes[1] == (byte)0xD8) {
            return "JPEG";
        } else if (bytes[0] == (byte)0x89 && bytes[1] == 'P' && bytes[2] == 'N' && bytes[3] == 'G') {
            return "PNG";
        } else if (bytes[0] == 'G' && bytes[1] == 'I' && bytes[2] == 'F') {
            return "GIF";
        } else if (bytes[0] == 'B' && bytes[1] == 'M') {
            return "BMP";
        }
        return null;
    }

    /**
     * Fallback：针对某些 ImageIO 解析失败的格式（如 CMYK JPEG），尝试手动解析
     */
    private static ImageInfo fallbackParse(byte[] bytes, String format) {
        return switch (format) {
            case "JPEG" -> parseJpegHeader(bytes);
            case "PNG" -> parsePngHeader(bytes);
            case "GIF" -> parseGifHeader(bytes);
            case "BMP" -> parseBmpHeader(bytes);
            default -> null;
        };
    }

    // 手动解析 JPEG 头（SOF0/SOF1 等段）
    private static ImageInfo parseJpegHeader(byte[] bytes) {
        int offset = 2;
        while (offset < bytes.length - 10) {
            if (bytes[offset] == (byte)0xFF) {
                int marker = bytes[offset + 1] & 0xFF;
                // SOF markers: Baseline DCT (0xC0), Progressive DCT (0xC2), etc.
                if ((marker >= 0xC0 && marker <= 0xC3) || (marker >= 0xC5 && marker <= 0xC7)) {
                    int length = ((bytes[offset + 2] & 0xFF) << 8) | (bytes[offset + 3] & 0xFF);
                    if (offset + 3 + length <= bytes.length) {
                        int height = ((bytes[offset + 5] & 0xFF) << 8) | (bytes[offset + 6] & 0xFF);
                        int width = ((bytes[offset + 7] & 0xFF) << 8) | (bytes[offset + 8] & 0xFF);
                        return new ImageInfo(width, height, bytes.length);
                    }
                }
                int length = ((bytes[offset + 2] & 0xFF) << 8) | (bytes[offset + 3] & 0xFF);
                offset += length + 2;
            } else {
                offset++;
            }
        }
        return null;
    }

    // 手动解析 PNG 头
    private static ImageInfo parsePngHeader(byte[] bytes) {
        if (bytes.length < 24) {return null;}
        int width = ((bytes[16] & 0xFF) << 24) | ((bytes[17] & 0xFF) << 16) | ((bytes[18] & 0xFF) << 8) | (bytes[19]
                                                                                                           & 0xFF);
        int height = ((bytes[20] & 0xFF) << 24) | ((bytes[21] & 0xFF) << 16) | ((bytes[22] & 0xFF) << 8) | (bytes[23]
                                                                                                            & 0xFF);
        return new ImageInfo(width, height, bytes.length);
    }

    // 手动解析 GIF 头
    private static ImageInfo parseGifHeader(byte[] bytes) {
        if (bytes.length < 10) {return null;}
        int width = ((bytes[6] & 0xFF) << 8) | (bytes[7] & 0xFF);
        int height = ((bytes[8] & 0xFF) << 8) | (bytes[9] & 0xFF);
        return new ImageInfo(width, height, bytes.length);
    }

    // 手动解析 BMP 头
    private static ImageInfo parseBmpHeader(byte[] bytes) {
        if (bytes.length < 26) {return null;}
        int width = ((bytes[18] & 0xFF)) | ((bytes[19] & 0xFF) << 8) | ((bytes[20] & 0xFF) << 16) | ((bytes[21] & 0xFF)
                                                                                                     << 24);
        int height = ((bytes[22] & 0xFF)) | ((bytes[23] & 0xFF) << 8) | ((bytes[24] & 0xFF) << 16) | ((bytes[25] & 0xFF)
                                                                                                      << 24);
        // 如果 height 为负，表示图像倒置，取绝对值
        height = Math.abs(height);
        return new ImageInfo(width, height, bytes.length);
    }
}