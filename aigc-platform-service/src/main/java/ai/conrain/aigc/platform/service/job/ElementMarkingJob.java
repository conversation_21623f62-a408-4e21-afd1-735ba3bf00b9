package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.dao.CreativeElementDAO;
import ai.conrain.aigc.platform.integration.gpt.AIService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.ShootingStyleService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.converter.CreativeElementConverter;
import ai.conrain.aigc.platform.service.model.query.CreativeElementQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.ModelTrainDetailVO;
import ai.conrain.aigc.platform.service.model.vo.ShootingStyleVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 模特/场景 风格场景标记任务
 * <p>
 * 该任务负责对系统中的模特/场景 进行风格标识标记和分类处理。通过 gpt 对风格进行识别，
 * 自动为模特/场景添加风格标签，便于后续的搜索、推荐和展示。
 * </p>
 */
@Slf4j
@Component
public class ElementMarkingJob extends JavaProcessor {
    private static final int IMAGE_NUM = 6;

    @Autowired
    private CreativeElementDAO creativeElementDAO;

    @Autowired
    private CreativeElementService creativeElementService;

    @Autowired
    private MaterialModelService materialModelService;

    @Autowired
    private ShootingStyleService shootingStyleService;

    @Autowired
    private AIService aiService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        // 解析任务参数，支持阿里云MSE定时任务参数传递
        Params params = new Params();
        String paramStr = StringUtils.isNotBlank(context.getInstanceParameters())
                ? context.getInstanceParameters()
                : context.getJobParameters();

        if (StringUtils.isNotBlank(paramStr)) {
            try {
                JSONObject paramJson = JSONObject.parseObject(paramStr);
                params = paramJson.toJavaObject(Params.class);
                // 确保forceRefresh有明确的默认值
                if (params.forceRefresh == null) {
                    params.forceRefresh = false;
                }
                log.info("[风格场景标记任务] 解析到任务参数：{}", paramJson.toJSONString());
            } catch (Exception e) {
                log.warn("[风格场景标记任务] 参数解析失败，使用默认参数：{}", e.getMessage());
                params.forceRefresh = false; // 设置默认值
            }
        } else {
            // 没有参数时设置默认值
            params.forceRefresh = false;
        }

        log.info("[风格场景标记任务] 开始执行，最终参数：{}", JSONObject.toJSONString(params));

        // 从文件中读取风格标记的prompt内容
        String prompt = shootingStyleService.getCachedStylePrompt();

        if (StringUtils.isBlank(prompt)) {
            return new ProcessResult(false, "无法读取风格标记 prompt 文件");
        }

        ExecutorService executor = Executors.newFixedThreadPool(5);

        try {
            executeElementStyleMarking(params, executor, prompt);
            log.info("[风格场景标记任务] 执行结束");
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("[风格场景标记任务] 执行过程中发生异常", e);
            return new ProcessResult(false, e.getMessage());
        } finally {
            MDC.remove("traceId");
            MDC.remove("env");
            OperationContextHolder.clean();
            executor.shutdown();
        }
    }

    /**
     * 执行创意元素风格标记
     */
    private void executeElementStyleMarking(Params params, ExecutorService executor, String prompt) {
        // 构建查询条件
        CreativeElementQuery query = new CreativeElementQuery();

        // 2级场景
        query.setLevel(2);
        // 只查询场景
        query.setConfigKey(ElementConfigKeyEnum.SCENE.name());
        // 只查询lora场景
        query.setIsLora(Boolean.TRUE);
        // 设置分页参数
        query.setPageSize(params.getPageSize());

        if (Boolean.TRUE.equals(params.getForceRefresh())) {
            // 强制刷新时查询所有记录
            query.setNeedInitStyleType(false);
            // 设置分页参数
            query.setPageSize(1000);

            // 获取所有风格类型，用于清理旧标签
            List<ShootingStyleVO> shootingStyleVOList = shootingStyleService.findAll();
            params.setStyleTypeList(shootingStyleVOList.stream().map(ShootingStyleVO::getType2Name).collect(Collectors.toList()));

            log.info("[风格场景标记任务] 强制刷新模式，将重新设置所有Lora场景的风格标签。");
        } else {
            query.setNeedInitStyleType(true);
            log.info("[风格场景标记任务] 增量模式，只设置未设置风格标签的场景。");
        }

        int currentPage = 1;
        int totalProcessed = 0;

        // 当强制刷新时，循环查询所有页面
        do {
            query.setPageNum(currentPage);
            PageInfo<CreativeElementVO> elements = creativeElementService.queryByPage(query);

            if (elements == null || elements.getList().isEmpty()) {
                log.info("[风格场景标记任务] 第{}页无数据，处理结束", currentPage);
                break;
            }

            log.info("[风格场景标记任务] 处理第{}页，获取到待处理的创意元素数量：{}", currentPage, elements.getList().size());

            elements.getList().stream()
                    .filter(Objects::nonNull)
                    .forEach(element -> {
                        try {
                            this.markElementStyle(
                                    executor,
                                    params,
                                    prompt,
                                    element.getLoraModelId() != null ? element.getLoraModelId() : 0,
                                    element,
                                    styleType -> {
                                        log.info("[风格场景标记任务][更新风格标签] elementId={} 识别到的风格类型：{}", element.getId(), styleType);
                                        // 重新查一遍，尽量避免脏数据覆盖
                                        CreativeElementVO latestElement;
                                        try {
                                            latestElement = creativeElementService.selectById(element.getId());
                                            if (latestElement == null) {
                                                log.warn("[风格场景标记任务] elementId={} 查询不到最新数据，跳过更新", element.getId());
                                                return;
                                            }
                                        } catch (Exception e) {
                                            log.error("[风格场景标记任务] elementId={} 查询最新数据失败：{}", element.getId(), e.getMessage());
                                            return;
                                        }

                                        // 获取现有的 type 数组
                                        List<String> currentTypes = latestElement.getType();
                                        if (currentTypes == null) {
                                            currentTypes = new ArrayList<>();
                                        } else {
                                            // 创建一个新的可变列表，避免修改原始列表
                                            currentTypes = new ArrayList<>(currentTypes);
                                        }

                                        // 如果是强制刷新模式，先移除所有可能的风格类型
                                        if (Boolean.TRUE.equals(params.getForceRefresh()) && params.getStyleTypeList() != null) {
                                            List<String> removedTypes = new ArrayList<>();
                                            for (String styleType2Remove : params.getStyleTypeList()) {
                                                if (currentTypes.remove(styleType2Remove)) {
                                                    removedTypes.add(styleType2Remove);
                                                }
                                            }
                                            if (!removedTypes.isEmpty()) {
                                                log.info("[风格场景标记任务][强制刷新] elementId={} 移除之前的风格类型：{}", element.getId(), removedTypes);
                                            }
                                        }

                                        if (StringUtils.isNotBlank(styleType)) {
                                            // 将识别到的风格类型添加到 type 数组中，去重
                                            Set<String> typeSet = new HashSet<>(currentTypes);
                                            typeSet.add(styleType);
                                            List<String> newTypes = new ArrayList<>(typeSet);

                                            // 更新 type 属性
                                            latestElement.setType(newTypes);

                                            // 保存风格类型到 extInfo（可选，用于记录）
                                            latestElement.addExtInfo(CommonConstants.KEY_STYLE_TYPE, styleType);
                                            latestElement.addExtInfo(CommonConstants.KEY_IS_SET_STYLE_TYPE, "Y");

                                            log.info("[风格场景标记任务] elementId={} 更新后的type：{}", element.getId(), newTypes);
                                        } else {
                                            // 即使没有识别到新的风格类型，也要记录处理状态
                                            latestElement.addExtInfo(CommonConstants.KEY_STYLE_TYPE, "");
                                            latestElement.addExtInfo(CommonConstants.KEY_IS_SET_STYLE_TYPE, "Y");

                                            // 更新type数组（已经移除了旧的风格标签）
                                            latestElement.setType(currentTypes);

                                            log.info("[风格场景标记任务] elementId={} 未识别到风格类型，已清理旧的风格标签", element.getId());
                                        }

                                        try {
                                            CreativeElementVO updateElement = new CreativeElementVO();
                                            updateElement.setId(latestElement.getId());
                                            updateElement.setType(latestElement.getType());
                                            updateElement.setExtInfo(latestElement.getExtInfo());
                                            creativeElementDAO.updateByPrimaryKeySelective(
                                                    CreativeElementConverter.vo2DO(updateElement));
                                            log.info("[风格场景标记任务] elementId={} 数据库更新成功", element.getId());
                                        } catch (Exception e) {
                                            log.error("[风格场景标记任务] elementId={} 数据库更新失败：{}", element.getId(), e.getMessage());
                                        }
                                    });
                        } catch (Exception e) {
                            log.error("[风格场景标记任务] elementId={} 处理过程中发生异常：{}", element.getId(), e.getMessage());
                        }
                    });


            totalProcessed += elements.getList().size();
            currentPage++;

            // 如果不是强制刷新模式，只处理第一页就退出
            if (!Boolean.TRUE.equals(params.getForceRefresh())) {
                break;
            }

            // 如果当前页数据量小于页大小，说明已经是最后一页
            if (elements.getList().size() < params.getPageSize()) {
                log.info("[风格场景标记任务] 第{}页数据量小于页大小，已处理完所有数据", currentPage - 1);
                break;
            }

        } while (Boolean.TRUE.equals(params.getForceRefresh()));

        log.info("[风格场景标记任务] 总共处理了{}条记录", totalProcessed);

        // 处理完成后统一刷新缓存
        if (totalProcessed > 0) {
            log.info("[风格场景标记任务] 处理完成，刷新风格分类提示词缓存");
            shootingStyleService.refreshStylePromptCache();
        }
    }

    /**
     * 标记创意元素风格
     */
    private void markElementStyle(ExecutorService executor,
                                  Params params,
                                  String prompt,
                                  int loraModelId,
                                  CreativeElementVO element,
                                  Consumer<String> styleConsumer) {

        ModelTrainDetailVO trainDetail;
        if (loraModelId == 0) {
            log.warn("[风格场景标记任务] loraModelId={} ", loraModelId);
            return;
        }

        try {
            trainDetail = materialModelService.getTrainDetail(loraModelId);
        } catch (Exception e) {
            log.warn("[风格场景标记任务] loraModelId={} 获取训练详情失败：{}", loraModelId, e.getMessage());
            return;
        }

        JSONObject materialDetail = trainDetail.getMaterialDetail();
        if (materialDetail == null) {
            log.warn("[风格场景标记任务] loraModelId={} 未获取到素材详情，跳过该任务", loraModelId);
            return;
        }

        // 从 materialDetail.imgUrls 中获取图片列表
        List<String> allImgUrls = Optional.ofNullable(materialDetail.getJSONArray("imgUrls"))
                .map(jsonArray -> jsonArray.toJavaList(String.class))
                .orElse(Collections.emptyList());

        if (allImgUrls.isEmpty()) {
            log.warn("[风格场景标记任务] loraModelId={} 图片列表为空，跳过该任务", loraModelId);
            styleConsumer.accept(null);
            return;
        }

        // 随机选择图片，最多选择 IMAGE_NUM 张
        List<String> imgUrls = new ArrayList<>(allImgUrls);
        Collections.shuffle(imgUrls);
        int selectCount = Math.min(IMAGE_NUM, imgUrls.size());
        imgUrls = imgUrls.subList(0, selectCount);

        // 使用 CompletableFuture 进行异步请求
        List<CompletableFuture<String>> futures = imgUrls.stream()
                .map(imgUrl -> CompletableFuture.supplyAsync(() -> {
                    try {
                        String text = aiService.chat(prompt, Collections.singletonList(imgUrl));
                        log.info("[风格场景标记任务] loraModelId={} imgUrl={} gptResponse={}", loraModelId, imgUrl, text);
                        return text;
                    } catch (Exception e) {
                        log.error("[风格场景标记任务] loraModelId={} imgUrl={} AI服务调用失败：{}", loraModelId, imgUrl, e.getMessage());
                        return null;
                    }
                }, executor))
                .collect(Collectors.toList());

        // 等待所有异步任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        // 获取所有任务的结果
        List<String> styleResults = allFutures.thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList()))
                .join();

        if (styleResults.isEmpty()) {
            log.warn("[风格场景标记任务] loraModelId={} 所有AI调用都失败了", loraModelId);
            styleConsumer.accept(null);
            return;
        }

        // 统计风格出现次数，取出现次数最多的风格作为最终结果
        String mostFrequentStyle = styleResults.stream()
                .filter(Objects::nonNull)  // 过滤掉null值
                .map(StringUtils::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.groupingBy(
                        Function.identity(),
                        HashMap::new,
                        Collectors.counting()))
                .entrySet()
                .stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);

        // 如果最终结果格式为 "ClothFirstCategory|二级分类"，则提取二级分类
        if (mostFrequentStyle != null && mostFrequentStyle.contains("|")) {
            String[] parts = mostFrequentStyle.split("\\|");
            mostFrequentStyle = parts.length > 1 ? parts[1].trim() : mostFrequentStyle;
        }

        log.info("[风格场景标记任务] loraModelId={} 统计结果：{}", loraModelId, styleResults);
        log.info("[风格场景标记任务] loraModelId={} 最终风格：{}", loraModelId, mostFrequentStyle);

        styleConsumer.accept(mostFrequentStyle);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Params {
        private List<Integer> elementIds;
        private Date startTime = new Date();
        private int pageSize = 10;
        private boolean random;
        private Boolean forceRefresh; // 从JobContext参数中获取，使用Boolean包装类型

        /**
         * 风格标签列表，全量时会使用
         */
        private List<String> styleTypeList;
    }
}
