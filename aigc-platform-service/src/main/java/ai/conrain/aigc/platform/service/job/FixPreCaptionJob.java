package ai.conrain.aigc.platform.service.job;


import ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import ai.conrain.aigc.platform.integration.ai.imageAnalysis.ImageQualityApiService;
import ai.conrain.aigc.platform.integration.ai.model.ImageSimpleCaption;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.onnx.GenreRecognizeOnnxService;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class FixPreCaptionJob extends JavaProcessor {

    @Autowired
    private ImageCaptionDAO imageCaptionDAO;

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageCaptionService imageCaptionService;

    @Autowired
    private ImageQualityApiService imageQualityService;

    @Autowired
    private GenreRecognizeOnnxService genreRecognizeOnnxService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        try {
            List<ImageCaptionDO> imageCaptionDOS = imageCaptionDAO.selectNoPreCaption(3000);

            if (CollectionUtils.isNotEmpty(imageCaptionDOS)) {
                for (ImageCaptionDO imageCaptionDO : imageCaptionDOS) {
                    ImageVO imageVO = imageService.selectById(imageCaptionDO.getImageId());
                    if (imageVO != null && imageVO.getMetadata() != null) {
                        ImageSimpleCaption preCaption = imageVO.getMetadata().toJavaObject(ImageSimpleCaption.class);
                        if (preCaption != null) {
                            if (StringUtils.isNotBlank(preCaption.getAge())
                                    || StringUtils.isNotBlank(preCaption.getTopCate1())
                                    || StringUtils.isNotBlank(preCaption.getUsageCate1())) {
                                IntegrationUtils.processNoneToNull(preCaption);

                                ImageCaptionVO target = new ImageCaptionVO();
                                target.setId(imageCaptionDO.getId());
                                target.setPreCaption(preCaption);
                                imageCaptionService.updateByIdSelective(target);
                            }
                        }
                    }
                }
            }
        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }
}
