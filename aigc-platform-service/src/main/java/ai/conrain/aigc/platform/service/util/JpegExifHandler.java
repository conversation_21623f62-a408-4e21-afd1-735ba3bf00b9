/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

//import javax.imageio.ImageIO;
//import javax.imageio.ImageWriteParam;
//import javax.imageio.ImageWriter;
//import javax.imageio.plugins.jpeg.JPEGImageWriteParam;
//import javax.imageio.stream.ImageOutputStream;
//
//import com.drew.imaging.ImageMetadataReader;
//import com.drew.imaging.ImageProcessingException;
//import com.drew.metadata.Directory;
//import com.drew.metadata.Metadata;
//import com.drew.metadata.exif.ExifIFD0Directory;
//import com.drew.metadata.exif.ExifSubIFDDirectory;
//import com.drew.metadata.jpeg.JpegDirectory;
//import java.awt.image.BufferedImage;
//import java.io.ByteArrayOutputStream;
//import java.io.File;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.nio.file.Files;
//import java.nio.file.Path;
//import java.nio.file.Paths;
//import java.nio.file.StandardCopyOption;
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.Iterator;
//import org.apache.commons.imaging.ImageFormat;
//import org.apache.commons.imaging.Imaging;
//import org.apache.commons.imaging.common.ImageMetadata;
//import org.apache.commons.imaging.formats.jpeg.JpegImageMetadata;
//import org.apache.commons.imaging.formats.jpeg.exif.ExifRewriter;
//import org.apache.commons.imaging.formats.tiff.TiffImageMetadata;
//import org.apache.commons.imaging.formats.tiff.write.TiffOutputSet;
//import org.apache.commons.imaging.formats.tiff.write.TiffOutputDirectory;
//import org.apache.commons.imaging.formats.tiff.constants.ExifTagConstants;
//import org.apache.commons.imaging.formats.tiff.constants.TiffTagConstants;
//import org.apache.commons.imaging.formats.tiff.fieldtypes.FieldTypeRational;
//import org.apache.commons.imaging.formats.tiff.write.TiffOutputField;
//import org.apache.commons.imaging.formats.tiff.taginfos.TagInfo;

/**
 *
 * <AUTHOR>
 * @version : JpegExifHandler.java, v 0.1 2025/8/31 12:51 renxiao.wu Exp $
 */
public class JpegExifHandler {
    //
    ///**
    // * 保存JPG图像并添加EXIF信息
    // *
    // * @param img                BufferedImage图像对象
    // * @param outputPath         输出文件路径
    // * @param referenceImagePath 参考图像路径（用于克隆EXIF）
    // */
    //public static void saveJpgWithExif(BufferedImage img, String outputPath, String referenceImagePath) {
    //    try {
    //        saveLosslessJpeg(img, outputPath, referenceImagePath);
    //        expandJpegSize(outputPath, outputPath, 0.2);
    //    } catch (Exception e) {
    //        System.err.println("[ERROR] 保存JPEG时出错: " + e.getMessage());
    //        e.printStackTrace();
    //    }
    //}
    //
    ///**
    // * 从参考图像中克隆EXIF数据
    // *
    // * @param referenceImagePath 参考图像文件路径
    // * @return TiffOutputSet EXIF数据集
    // */
    //public static TiffOutputSet cloneExifFromImage(String referenceImagePath) {
    //    System.out.println("[DEBUG] 开始从参考图像读取EXIF: " + referenceImagePath);
    //
    //    // 检查文件是否存在
    //    if (!Files.exists(Paths.get(referenceImagePath))) {
    //        System.err.println("[ERROR] 参考图像文件不存在: " + referenceImagePath);
    //        return createDefaultExif();
    //    }
    //
    //    try {
    //        File imageFile = new File(referenceImagePath);
    //        ImageMetadata metadata = Imaging.getMetadata(imageFile);
    //
    //        if (metadata instanceof JpegImageMetadata) {
    //            JpegImageMetadata jpegMetadata = (JpegImageMetadata)metadata;
    //            TiffImageMetadata exif = jpegMetadata.getExif();
    //
    //            if (exif != null) {
    //                TiffOutputSet outputSet = exif.getOutputSet();
    //
    //                if (outputSet != null) {
    //                    // 更新时间戳为当前时间
    //                    updateTimestamps(outputSet);
    //
    //                    // 打印关键EXIF信息
    //                    printExifInfo(outputSet);
    //
    //                    return outputSet;
    //                }
    //            }
    //        }
    //
    //        System.out.println("[WARNING] 参考图像 " + referenceImagePath + " 没有EXIF数据");
    //        return createDefaultExif();
    //
    //    } catch (Exception e) {
    //        System.err.println("[ERROR] 读取参考图像EXIF失败: " + e.getMessage());
    //        return createDefaultExif();
    //    }
    //}
    //
    ///**
    // * 安全添加EXIF字段的辅助方法
    // */
    //private static void safeAddField(TiffOutputDirectory directory, TagInfo tagInfo, Object value) {
    //    try {
    //        directory.removeField(tagInfo);
    //
    //        // 使用Apache Commons Imaging的简化API来添加字段
    //        if (value instanceof String) {
    //            directory.add(tagInfo, (String) value);
    //        } else if (value instanceof Short) {
    //            directory.add(tagInfo, (Short) value);
    //        } else {
    //            directory.add(tagInfo, value);
    //        }
    //    } catch (Exception e) {
    //        System.err.println("[WARNING] 添加EXIF字段失败 " + tagInfo.name + ": " + e.getMessage());
    //    }
    //}
    //
    ///**
    // * 安全添加有理数EXIF字段的辅助方法
    // */
    //private static void safeAddRationalField(TiffOutputDirectory directory, TagInfo tagInfo, int numerator,
    //                                         int denominator) {
    //    try {
    //        directory.removeField(tagInfo);
    //
    //        // 使用Apache Commons Imaging的有理数API
    //        FieldTypeRational.RationalNumber rational = new FieldTypeRational.RationalNumber(numerator, denominator);
    //        directory.add(tagInfo, rational);
    //    } catch (Exception e) {
    //        System.err.println("[WARNING] 添加有理数EXIF字段失败 " + tagInfo.name + ": " + e.getMessage());
    //    }
    //}
    //
    ///**
    // * 创建默认的EXIF数据
    // *
    // * @return TiffOutputSet 默认EXIF数据集
    // */
    //public static TiffOutputSet createDefaultExif() {
    //    try {
    //        TiffOutputSet outputSet = new TiffOutputSet();
    //        TiffOutputDirectory exifDirectory = outputSet.getOrCreateExifDirectory();
    //        TiffOutputDirectory rootDirectory = outputSet.getOrCreateRootDirectory();
    //
    //        String currentTime = new SimpleDateFormat("yyyy:MM:dd HH:mm:ss").format(new Date());
    //
    //        // 设置相机信息
    //        safeAddField(rootDirectory, TiffTagConstants.TIFF_TAG_MAKE, "Canon");
    //        safeAddField(rootDirectory, TiffTagConstants.TIFF_TAG_MODEL, "Canon EOS 5D Mark IV");
    //        safeAddField(rootDirectory, TiffTagConstants.TIFF_TAG_DATE_TIME, currentTime);
    //
    //        // 分辨率需要使用有理数格式
    //        safeAddRationalField(rootDirectory, TiffTagConstants.TIFF_TAG_XRESOLUTION, 300, 1);
    //        safeAddRationalField(rootDirectory, TiffTagConstants.TIFF_TAG_YRESOLUTION, 300, 1);
    //        safeAddField(rootDirectory, TiffTagConstants.TIFF_TAG_ORIENTATION, (short)1);
    //
    //        // 设置拍摄参数
    //        safeAddField(exifDirectory, ExifTagConstants.EXIF_TAG_DATE_TIME_ORIGINAL, currentTime);
    //        // EXIF_TAG_DATE_TIME_DIGITIZED 在Apache Sanselan 0.97版本中不存在，跳过
    //
    //        // 曝光时间使用有理数格式 (1/320)
    //        safeAddRationalField(exifDirectory, ExifTagConstants.EXIF_TAG_EXPOSURE_TIME, 1, 320);
    //
    //        // 光圈值使用有理数格式 (f/5.6)
    //        safeAddRationalField(exifDirectory, ExifTagConstants.EXIF_TAG_FNUMBER, 56, 10);
    //
    //        safeAddField(exifDirectory, ExifTagConstants.EXIF_TAG_ISO, (short)500);
    //        // EXIF_TAG_LENS_MODEL 在Apache Sanselan 0.97版本中不存在，跳过
    //
    //        // 焦距使用有理数格式 (45mm)
    //        safeAddRationalField(exifDirectory, ExifTagConstants.EXIF_TAG_FOCAL_LENGTH, 450, 10);
    //
    //        safeAddField(exifDirectory, ExifTagConstants.EXIF_TAG_FLASH, (short)0);
    //        safeAddField(exifDirectory, ExifTagConstants.EXIF_TAG_METERING_MODE, (short)5);
    //
    //        return outputSet;
    //
    //    } catch (Exception e) {
    //        System.err.println("[ERROR] 创建默认EXIF失败: " + e.getMessage());
    //        return new TiffOutputSet();
    //    }
    //}
    //
    ///**
    // * 解析参考图像路径，处理相对路径问题
    // *
    // * @param referenceImagePath 参考图像路径（可能是相对路径）
    // * @return 绝对路径字符串
    // */
    //public static String resolveReferenceImagePath(String referenceImagePath) {
    //    if (referenceImagePath == null || referenceImagePath.isEmpty()) {
    //        return null;
    //    }
    //
    //    Path path = Paths.get(referenceImagePath);
    //
    //    // 如果已经是绝对路径，直接返回
    //    if (path.isAbsolute()) {
    //        return path.toString();
    //    }
    //
    //    // 如果是相对路径，基于当前工作目录来解析
    //    Path currentDir = Paths.get("").toAbsolutePath();
    //    Path absolutePath = currentDir.resolve(path).normalize();
    //
    //    System.out.println("[INFO] 相对路径转换: " + referenceImagePath + " -> " + absolutePath);
    //    return absolutePath.toString();
    //}
    //
    ///**
    // * 获取EXIF数据，优先级：参考图像 > 默认EXIF
    // *
    // * @param referenceImagePath 参考图像路径
    // * @return TiffOutputSet EXIF数据集
    // */
    //public static TiffOutputSet getExifData(String referenceImagePath) {
    //    // 解析参考图像路径
    //    String resolvedPath = resolveReferenceImagePath(referenceImagePath);
    //
    //    // 优先使用参考图像
    //    if (resolvedPath != null && Files.exists(Paths.get(resolvedPath))) {
    //        System.out.println("[INFO] 使用参考图像的EXIF数据: " + resolvedPath);
    //        return cloneExifFromImage(resolvedPath);
    //    }
    //
    //    // 如果提供了路径但文件不存在，给出详细的错误信息
    //    if (referenceImagePath != null && !referenceImagePath.isEmpty()) {
    //        System.out.println("[WARNING] 参考图像文件不存在:");
    //        System.out.println("[WARNING]   原始路径: " + referenceImagePath);
    //        System.out.println("[WARNING]   解析后路径: " + resolvedPath);
    //        System.out.println("[WARNING]   当前工作目录: " + System.getProperty("user.dir"));
    //        System.out.println("[INFO] 将使用默认EXIF数据");
    //    }
    //
    //    // 使用默认EXIF
    //    System.out.println("[INFO] 使用默认EXIF数据");
    //    return createDefaultExif();
    //}
    //
    ///**
    // * 保存无损JPEG图像并添加EXIF信息
    // *
    // * @param img                BufferedImage图像对象
    // * @param outputPath         输出文件路径
    // * @param referenceImagePath 参考图像路径（用于克隆EXIF）
    // */
    //public static void saveLosslessJpeg(BufferedImage img, String outputPath, String referenceImagePath) {
    //    try {
    //        // 获取EXIF数据
    //        TiffOutputSet outputSet = getExifData(referenceImagePath);
    //
    //        // 确保Orientation设置为1
    //        if (outputSet != null) {
    //            try {
    //                TiffOutputDirectory rootDirectory = outputSet.getOrCreateRootDirectory();
    //                safeAddField(rootDirectory, TiffTagConstants.TIFF_TAG_ORIENTATION, (short)1);
    //            } catch (Exception e) {
    //                System.out.println("[WARNING] 修改EXIF数据失败: " + e.getMessage());
    //            }
    //        }
    //
    //        // 创建临时文件保存中间结果
    //        File tempFile = File.createTempFile("temp_jpeg", ".jpg");
    //
    //        // 使用最高质量保存JPEG
    //        saveHighQualityJpeg(img, tempFile.getAbsolutePath());
    //
    //        // 添加EXIF数据
    //        if (outputSet != null) {
    //            try {
    //                byte[] imageBytes = Files.readAllBytes(tempFile.toPath());
    //                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    //
    //                new ExifRewriter().updateExifMetadataLossless(imageBytes, outputStream, outputSet);
    //
    //                // 写入最终文件
    //                Files.write(Paths.get(outputPath), outputStream.toByteArray());
    //
    //            } catch (Exception e) {
    //                System.err.println("[WARNING] 添加EXIF失败，使用原始图像: " + e.getMessage());
    //                Files.copy(tempFile.toPath(), Paths.get(outputPath), StandardCopyOption.REPLACE_EXISTING);
    //            }
    //        } else {
    //            Files.copy(tempFile.toPath(), Paths.get(outputPath), StandardCopyOption.REPLACE_EXISTING);
    //        }
    //
    //        // 删除临时文件
    //        tempFile.delete();
    //
    //        // 验证保存结果
    //        verifyOutput(outputPath);
    //
    //    } catch (Exception e) {
    //        System.err.println("[ERROR] 保存无损JPEG失败: " + e.getMessage());
    //        e.printStackTrace();
    //    }
    //}
    //
    ///**
    // * 保存高质量JPEG图像
    // */
    //private static void saveHighQualityJpeg(BufferedImage img, String outputPath) throws IOException {
    //    Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpeg");
    //    if (!writers.hasNext()) {
    //        throw new IllegalStateException("没有找到JPEG写入器");
    //    }
    //
    //    ImageWriter writer = writers.next();
    //    JPEGImageWriteParam writeParam = (JPEGImageWriteParam)writer.getDefaultWriteParam();
    //
    //    // 设置最高质量参数
    //    writeParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
    //    writeParam.setCompressionQuality(1.0f); // 最高质量
    //    writeParam.setOptimizeHuffmanTables(false); // 禁用霍夫曼编码优化
    //    writeParam.setProgressiveMode(ImageWriteParam.MODE_DISABLED); // 禁用渐进式
    //
    //    try (ImageOutputStream ios = ImageIO.createImageOutputStream(new File(outputPath))) {
    //        writer.setOutput(ios);
    //        writer.write(null, new javax.imageio.IIOImage(img, null, null), writeParam);
    //    } finally {
    //        writer.dispose();
    //    }
    //}
    //
    ///**
    // * 按中间文件体积的指定比例扩容
    // *
    // * @param intermediatePath 中间文件路径
    // * @param finalPath        最终文件路径
    // * @param ratio            扩容比例
    // */
    //public static void expandJpegSize(String intermediatePath, String finalPath, double ratio) {
    //    try {
    //        // 读取中间文件
    //        byte[] data = Files.readAllBytes(Paths.get(intermediatePath));
    //
    //        // 定位JPEG结束标记 (0xFF 0xD9)
    //        int eofPos = -1;
    //        for (int i = data.length - 2; i >= 0; i--) {
    //            if ((data[i] & 0xFF) == 0xFF && (data[i + 1] & 0xFF) == 0xD9) {
    //                eofPos = i;
    //                break;
    //            }
    //        }
    //
    //        if (eofPos == -1) {
    //            throw new IllegalArgumentException("无效的JPEG文件结构");
    //        }
    //
    //        // 计算扩容量
    //        int baseSize = data.length;
    //        int paddingSize = (int)(baseSize * ratio);
    //
    //        // 构造填充数据（零填充）
    //        byte[] padding = new byte[paddingSize];
    //
    //        // 生成最终文件
    //        try (FileOutputStream fos = new FileOutputStream(finalPath)) {
    //            fos.write(data, 0, eofPos + 2); // 写入到EOF标记（包含）
    //            fos.write(padding); // 写入填充数据
    //        }
    //
    //        System.out.println("[INFO] 文件扩容完成: " + baseSize + " -> " + (baseSize + paddingSize) + " bytes");
    //
    //    } catch (Exception e) {
    //        System.err.println("[ERROR] 扩容JPEG文件失败: " + e.getMessage());
    //        e.printStackTrace();
    //    }
    //}
    //
    ///**
    // * 更新EXIF时间戳为当前时间
    // */
    //private static void updateTimestamps(TiffOutputSet outputSet) {
    //    try {
    //        String currentTime = new SimpleDateFormat("yyyy:MM:dd HH:mm:ss").format(new Date());
    //
    //        TiffOutputDirectory rootDirectory = outputSet.getOrCreateRootDirectory();
    //        TiffOutputDirectory exifDirectory = outputSet.getOrCreateExifDirectory();
    //
    //        // 更新时间戳
    //        safeAddField(rootDirectory, TiffTagConstants.TIFF_TAG_DATE_TIME, currentTime);
    //        safeAddField(exifDirectory, ExifTagConstants.EXIF_TAG_DATE_TIME_ORIGINAL, currentTime);
    //        // EXIF_TAG_DATE_TIME_DIGITIZED 在Apache Sanselan 0.97版本中不存在，跳过
    //
    //    } catch (Exception e) {
    //        System.err.println("[WARNING] 更新时间戳失败: " + e.getMessage());
    //    }
    //}
    //
    ///**
    // * 打印EXIF关键信息
    // */
    //private static void printExifInfo(TiffOutputSet outputSet) {
    //    try {
    //        TiffOutputDirectory rootDirectory = outputSet.getRootDirectory();
    //        TiffOutputDirectory exifDirectory = outputSet.getExifDirectory();
    //
    //        if (rootDirectory != null) {
    //            System.out.println(
    //                "[INFO] 相机制造商: " + getFieldValue(rootDirectory, TiffTagConstants.TIFF_TAG_MAKE));
    //            System.out.println("[INFO] 相机型号: " + getFieldValue(rootDirectory, TiffTagConstants.TIFF_TAG_MODEL));
    //        }
    //
    //        if (exifDirectory != null) {
    //            System.out.println("[INFO] ISO: " + getFieldValue(exifDirectory, ExifTagConstants.EXIF_TAG_ISO));
    //            // EXIF_TAG_LENS_MODEL 在Apache Sanselan 0.97版本中不存在，跳过
    //        }
    //
    //    } catch (Exception e) {
    //        System.err.println("[WARNING] 打印EXIF信息失败: " + e.getMessage());
    //    }
    //}
    //
    ///**
    // * 获取EXIF字段值的辅助方法
    // */
    //private static String getFieldValue(TiffOutputDirectory directory, TagInfo tag) {
    //    try {
    //        TiffOutputField field = directory.findField(tag);
    //        return field != null ? field.tagInfo.getDescription() : "未知";
    //    } catch (Exception e) {
    //        return "未知";
    //    }
    //}
    //
    ///**
    // * 验证输出文件
    // */
    //private static void verifyOutput(String outputPath) {
    //    try {
    //        File outputFile = new File(outputPath);
    //        if (outputFile.exists()) {
    //            long fileSize = outputFile.length();
    //            System.out.println("[SUCCESS] 文件已保存: " + outputPath + " (大小: " + fileSize + " bytes)");
    //
    //            // 验证EXIF是否正确写入
    //            try {
    //                Metadata metadata = ImageMetadataReader.readMetadata(outputFile);
    //                ExifIFD0Directory exifIFD0 = metadata.getFirstDirectoryOfType(ExifIFD0Directory.class);
    //
    //                if (exifIFD0 != null) {
    //                    String make = exifIFD0.getString(ExifIFD0Directory.TAG_MAKE);
    //                    String model = exifIFD0.getString(ExifIFD0Directory.TAG_MODEL);
    //                    int orientation = exifIFD0.getInt(ExifIFD0Directory.TAG_ORIENTATION);
    //
    //                    System.out.println(
    //                        "[VERIFY] EXIF验证成功 - 制造商: " + make + ", 型号: " + model + ", 方向: " + orientation);
    //                } else {
    //                    System.out.println("[WARNING] 验证: 输出图像不包含EXIF数据！");
    //                }
    //            } catch (Exception e) {
    //                System.err.println("[ERROR] 验证EXIF时出错: " + e.getMessage());
    //            }
    //        } else {
    //            System.err.println("[ERROR] 文件写入失败: " + outputPath);
    //        }
    //    } catch (Exception e) {
    //        System.err.println("[ERROR] 验证输出失败: " + e.getMessage());
    //    }
    //}
    //
    ///**
    // * 主函数 - 测试程序
    // */
    //public static void main(String[] args) {
    //    System.out.println("[INFO] 开始JPEG保存程序");
    //
    //    // 测试路径解析功能
    //    String testRelativePath = "./exifData/IMG_8489.jpeg";
    //    String resolvedPath = resolveReferenceImagePath(testRelativePath);
    //    System.out.println("[TEST] 路径解析测试:");
    //    System.out.println("[TEST]   相对路径: " + testRelativePath);
    //    System.out.println("[TEST]   解析后路径: " + resolvedPath);
    //    System.out.println("[TEST]   文件是否存在: " + (resolvedPath != null && Files.exists(Paths.get(resolvedPath))));
    //    System.out.println();
    //
    //    // 示例用法 - 请根据实际情况修改路径
    //    String inputImagePath = "input.jpg";  // 修改为您的输入图像路径
    //    String outputImagePath = "output_with_exif.jpg"; // 输出路径
    //    String referenceImagePath = "./exifData/1.jpeg"; // 参考图像路径
    //
    //    System.out.println("[INFO] 输入图像: " + inputImagePath);
    //    System.out.println("[INFO] 输出图像: " + outputImagePath);
    //    System.out.println("[INFO] 参考图像: " + referenceImagePath);
    //
    //    try {
    //        // 读取输入图像
    //        BufferedImage img = ImageIO.read(new File(inputImagePath));
    //        if (img == null) {
    //            System.err.println("[ERROR] 无法读取输入图像: " + inputImagePath);
    //            return;
    //        }
    //
    //        System.out.println("[INFO] 成功打开输入图像: " + img.getWidth() + "x" + img.getHeight());
    //
    //        // 使用参考图像克隆EXIF
    //        saveJpgWithExif(img, outputImagePath, referenceImagePath);
    //
    //        System.out.println("[SUCCESS] 图像已保存到: " + outputImagePath);
    //
    //    } catch (IOException e) {
    //        System.err.println("[ERROR] 输入文件不存在或无法读取: " + inputImagePath);
    //        System.out.println("[INFO] 请确保文件路径正确");
    //    } catch (Exception e) {
    //        System.err.println("[ERROR] 处理过程中出错: " + e.getMessage());
    //        e.printStackTrace();
    //    }
    //}
}