package ai.conrain.aigc.platform.service.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 服装规则查询结果封装类
 * 用于封装样式白名单、构图黑名单和用途白名单的查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClothingRuleResult {

    /**
     * 样式白名单 - 可作为参考图的服装样式列表
     */
    private List<String> styleWhiteList;

    /**
     * 构图黑名单 - 不适合的拍摄构图列表
     */
    private List<String> compositionBlackList;

    /**
     * 用途白名单 - 服装用途分类列表
     */
    private List<String> usageWhiteList;

    /**
     * 检查样式白名单是否为空
     * 
     * @return 如果样式白名单为空或null返回true，否则返回false
     */
    public boolean isStyleWhiteListEmpty() {
        return styleWhiteList == null || styleWhiteList.isEmpty();
    }

    /**
     * 检查构图黑名单是否为空
     * 
     * @return 如果构图黑名单为空或null返回true，否则返回false
     */
    public boolean isCompositionBlackListEmpty() {
        return compositionBlackList == null || compositionBlackList.isEmpty();
    }

    /**
     * 检查用途白名单是否为空
     * 
     * @return 如果用途白名单为空或null返回true，否则返回false
     */
    public boolean isUsageWhiteListEmpty() {
        return usageWhiteList == null || usageWhiteList.isEmpty();
    }

    /**
     * 获取样式白名单大小
     * 
     * @return 样式白名单元素个数
     */
    public int getStyleWhiteListSize() {
        return styleWhiteList == null ? 0 : styleWhiteList.size();
    }

    /**
     * 获取构图黑名单大小
     * 
     * @return 构图黑名单元素个数
     */
    public int getCompositionBlackListSize() {
        return compositionBlackList == null ? 0 : compositionBlackList.size();
    }

    /**
     * 获取用途白名单大小
     * 
     * @return 用途白名单元素个数
     */
    public int getUsageWhiteListSize() {
        return usageWhiteList == null ? 0 : usageWhiteList.size();
    }
}